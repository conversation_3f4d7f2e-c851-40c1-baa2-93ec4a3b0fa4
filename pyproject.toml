[tool.poetry]
name = "jenkins-reader-agent"
version = "0.1.0"
description = "A comprehensive Jenkins read-only agent built using Google's Agent Development Kit (ADK)"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "jenkins_agent"}]

[tool.poetry.dependencies]
python = "^3.11"
google-adk = "^0.1.0"
python-jenkins = "^1.8.0"
google-cloud-secret-manager = "^2.16.0"
google-cloud-iam = "^2.12.0"
google-cloud-logging = "^3.8.0"
pydantic = "^2.5.0"
pydantic-settings = "^2.0.0"
httpx = "^0.28.1"
tenacity = "^8.2.0"
structlog = "^23.2.0"
asyncio-throttle = "^1.0.2"
xmltodict = "^0.13.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
pytest-cov = "^4.1.0"
pytest-mock = "^3.11.1"
black = "^23.7.0"
isort = "^5.12.0"
flake8 = "^6.0.0"
mypy = "^1.5.0"
bandit = "^1.7.5"
pre-commit = "^3.3.3"

[tool.poetry.group.test.dependencies]
responses = "^0.23.3"
factory-boy = "^3.3.0"
freezegun = "^1.2.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["jenkins_agent"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "jenkins.*",
    "google.adk.*",
    "google.cloud.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "security: Security tests",
    "slow: Slow running tests",
]

[tool.coverage.run]
source = ["jenkins_agent"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "test_*"]
skips = ["B101", "B601"]
