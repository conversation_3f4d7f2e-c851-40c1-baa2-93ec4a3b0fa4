#!/usr/bin/env python3
"""
Basic Usage Example for ADK Analyst Jenkins Reader Agent

This script demonstrates how to use the Jenkins Reader Agent
for basic Jenkins analysis tasks.
"""

import asyncio
import os
import sys
from typing import Dict, List

# Add the parent directory to the path to import jenkins_agent
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from jenkins_agent.agent import root_agent


async def basic_jenkins_analysis():
    """Demonstrate basic Jenkins analysis capabilities."""
    
    print("🔍 ADK Analyst - Jenkins Reader Agent")
    print("=====================================")
    print()
    
    # Example queries to demonstrate agent capabilities
    queries = [
        "What tools do you have available for Jenkins analysis?",
        "How can you help me analyze my Jenkins infrastructure?",
        "What security measures do you implement?",
        "Can you explain your read-only approach to Jenkins analysis?",
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"📝 Query {i}: {query}")
        print("-" * 60)
        
        try:
            # Use the agent to process the query
            response_parts = []
            async for chunk in root_agent.run_async(query):
                response_parts.append(str(chunk))
            
            response = "".join(response_parts)
            
            # Display the response (truncated for readability)
            if len(response) > 500:
                print(f"💬 Response: {response[:500]}...")
            else:
                print(f"💬 Response: {response}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print()
        print("=" * 60)
        print()


async def jenkins_connection_example():
    """Example of how to validate Jenkins connection."""
    
    print("🔗 Jenkins Connection Validation Example")
    print("========================================")
    print()
    
    # Example Jenkins URLs (replace with your actual Jenkins instance)
    jenkins_urls = [
        "https://jenkins.example.com",
        "https://ci.yourcompany.com",
        "http://localhost:8080"
    ]
    
    for url in jenkins_urls:
        print(f"🌐 Testing connection to: {url}")
        
        query = f"Can you validate the connection to Jenkins at {url}?"
        
        try:
            response_parts = []
            async for chunk in root_agent.run_async(query):
                response_parts.append(str(chunk))
            
            response = "".join(response_parts)
            print(f"📊 Result: {response[:200]}...")
            
        except Exception as e:
            print(f"❌ Connection failed: {e}")
        
        print()


async def jenkins_jobs_analysis_example():
    """Example of Jenkins jobs analysis."""
    
    print("📋 Jenkins Jobs Analysis Example")
    print("================================")
    print()
    
    analysis_queries = [
        "How would you analyze Jenkins job performance?",
        "What metrics do you look for in build history?",
        "How can you identify failing jobs and their patterns?",
        "What recommendations would you provide for CI/CD optimization?"
    ]
    
    for query in analysis_queries:
        print(f"🔍 Analysis Query: {query}")
        print("-" * 50)
        
        try:
            response_parts = []
            async for chunk in root_agent.run_async(query):
                response_parts.append(str(chunk))
            
            response = "".join(response_parts)
            print(f"📈 Analysis: {response[:300]}...")
            
        except Exception as e:
            print(f"❌ Analysis failed: {e}")
        
        print()


def display_agent_info():
    """Display information about the agent configuration."""
    
    print("ℹ️  Agent Information")
    print("====================")
    print()
    
    print(f"🤖 Agent Name: {root_agent.name}")
    print(f"🧠 Model: {root_agent.model}")
    print(f"🛠️  Number of Tools: {len(root_agent.tools)}")
    print()
    
    print("📚 Available Tools:")
    for tool in root_agent.tools:
        print(f"   • {tool.__name__}")
    print()
    
    print("🔒 Security Features:")
    print("   • Read-only access to Jenkins")
    print("   • Input validation and sanitization")
    print("   • Audit logging for all operations")
    print("   • Rate limiting to prevent abuse")
    print("   • Secure credential handling")
    print()


async def interactive_mode():
    """Interactive mode for custom queries."""
    
    print("💬 Interactive Mode")
    print("==================")
    print("Enter your Jenkins-related questions (type 'quit' to exit)")
    print()
    
    while True:
        try:
            query = input("🤔 Your question: ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not query:
                continue
            
            print("🤖 Agent is thinking...")
            
            response_parts = []
            async for chunk in root_agent.run_async(query):
                response_parts.append(str(chunk))
            
            response = "".join(response_parts)
            print(f"💡 Response: {response}")
            print()
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            print()


async def main():
    """Main function to run all examples."""
    
    print("🚀 ADK Analyst Jenkins Reader Agent - Usage Examples")
    print("====================================================")
    print()
    
    # Check environment
    print("🔧 Environment Check:")
    print(f"   Python Version: {sys.version}")
    print(f"   Working Directory: {os.getcwd()}")
    print(f"   Google Credentials: {'✅ Set' if os.getenv('GOOGLE_APPLICATION_CREDENTIALS') else '❌ Not Set'}")
    print()
    
    # Display agent information
    display_agent_info()
    
    # Run examples
    try:
        print("🎯 Running Basic Analysis Examples...")
        await basic_jenkins_analysis()
        
        print("🎯 Running Connection Examples...")
        await jenkins_connection_example()
        
        print("🎯 Running Jobs Analysis Examples...")
        await jenkins_jobs_analysis_example()
        
        # Ask if user wants interactive mode
        response = input("Would you like to try interactive mode? (y/n): ").strip().lower()
        if response in ['y', 'yes']:
            await interactive_mode()
        
    except KeyboardInterrupt:
        print("\n⏹️  Examples interrupted by user")
    except Exception as e:
        print(f"\n❌ Examples failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Set up environment if not already configured
    if not os.getenv('GOOGLE_APPLICATION_CREDENTIALS'):
        print("⚠️  Warning: GOOGLE_APPLICATION_CREDENTIALS not set")
        print("   Please set up your Google Cloud credentials first")
        print("   See docs/setup.md for instructions")
        print()
    
    # Run the examples
    asyncio.run(main())
