# Setup Guide - ADK Analyst Jenkins Reader Agent

This guide provides detailed instructions for setting up and configuring the Jenkins Reader Agent.

## Prerequisites

### System Requirements

- **Python**: 3.12 or higher
- **Memory**: Minimum 2GB RAM, recommended 4GB+
- **Storage**: 1GB free space for dependencies and logs
- **Network**: Internet access for Google Cloud APIs and Jenkins connectivity

### Required Services

1. **Google Cloud Project**
   - Vertex AI API enabled
   - Service account with appropriate permissions
   - Billing account configured

2. **Jenkins Server**
   - Jenkins instance accessible via HTTP/HTTPS
   - Valid user credentials with read permissions
   - API access enabled

## Installation

### 1. Clone Repository

```bash
<NAME_EMAIL>:truxt-ai/adk-analyst.git
cd adk-analyst
```

### 2. Python Environment Setup

#### Option A: Virtual Environment (Recommended)
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

#### Option B: Poetry (Alternative)
```bash
poetry install
poetry shell
```

### 3. Google Cloud Configuration

#### Service Account Setup
1. Create a service account in Google Cloud Console
2. <PERSON> the following roles:
   - `Vertex AI User`
   - `AI Platform Developer`
   - `Logs Writer`
3. Download the service account key as JSON
4. Place the key file in the project root as `service-account-key.json`

#### Environment Variables
```bash
export GOOGLE_APPLICATION_CREDENTIALS=./service-account-key.json
export GOOGLE_CLOUD_PROJECT=your-project-id
export GOOGLE_CLOUD_LOCATION=us-central1
```

### 4. Application Configuration

#### Environment File
Copy the example environment file and configure:
```bash
cp .env.example .env
```

Edit `.env` with your specific configuration:
```bash
# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_GENAI_USE_VERTEXAI=TRUE

# Jenkins Configuration
JENKINS_CREDENTIALS_SECRET=jenkins-credentials
ALLOWED_JENKINS_DOMAINS=jenkins.example.com,localhost
JENKINS_PASSWORD=your-jenkins-password

# Application Configuration
LOG_LEVEL=INFO
MAX_CONCURRENT_REQUESTS=10
RATE_LIMIT_PER_MINUTE=100

# Security Configuration
ENABLE_AUDIT_LOGGING=true
ENABLE_RATE_LIMITING=true
MAX_RESULTS_PER_QUERY=1000
```

## Verification

### 1. Basic Functionality Test
```bash
python test_simple_cli.py
```

### 2. Web Interface Test
```bash
python -m google.adk.cli web . --port 8000
```

### 3. Docker Test
```bash
docker build -f Dockerfile.adk -t jenkins-reader-agent .
docker run -p 8001:8000 jenkins-reader-agent
```

## Troubleshooting

### Common Issues

#### 1. Authentication Errors
**Problem**: `google.auth.exceptions.DefaultCredentialsError`
**Solution**: 
- Verify `GOOGLE_APPLICATION_CREDENTIALS` is set correctly
- Check service account key file exists and is valid
- Ensure service account has required permissions

#### 2. Import Errors
**Problem**: `ModuleNotFoundError: No module named 'google.adk'`
**Solution**:
- Verify virtual environment is activated
- Run `pip install -r requirements.txt`
- Check Python version is 3.12+

#### 3. Jenkins Connection Issues
**Problem**: Connection timeouts or authentication failures
**Solution**:
- Verify Jenkins URL is accessible
- Check Jenkins credentials are correct
- Ensure Jenkins API is enabled
- Verify network connectivity

## Security Considerations

### Credential Management
- Never commit `service-account-key.json` to version control
- Use environment variables for sensitive configuration
- Rotate service account keys regularly
- Implement least-privilege access principles

### Network Security
- Use HTTPS for Jenkins connections
- Implement proper firewall rules
- Consider VPN or private network access
- Enable audit logging for compliance

### Data Protection
- All Jenkins data is read-only
- No persistent storage of sensitive data
- Audit logs track all operations
- Rate limiting prevents abuse

## Next Steps

1. **Production Deployment**: See [Docker Deployment Guide](../README.md#docker-deployment)
2. **Monitoring Setup**: Configure logging and metrics collection
3. **Security Hardening**: Implement additional security measures
4. **Performance Tuning**: Optimize for your specific use case

For additional help, refer to:
- [Design Overview](design.md)
- [Test Results](../TEST_RESULTS.md)
- [Development Guide](development.md)
