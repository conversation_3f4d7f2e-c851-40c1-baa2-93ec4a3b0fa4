# Custom Domain Setup Guide - agents.trucks.ai

This guide explains how to set up the custom domain `agents.trucks.ai` for the ADK Analyst Jenkins Reader Agent running on Google Cloud Run.

## 🎯 Overview

We'll configure:
- **Domain**: `agents.trucks.ai`
- **Service**: ADK Analyst Jenkins Reader Agent
- **Platform**: Google Cloud Run
- **DNS Provider**: Cloudflare
- **SSL**: Automatic Google-managed certificate

## 🚀 Quick Setup

### Step 1: Run the Domain Setup Script
```bash
./gcp-deployment/setup-custom-domain.sh agents.trucks.ai
```

### Step 2: Add CNAME Record in Cloudflare
```
Type: CNAME
Name: agents
Target: [provided by the script]
TTL: Auto
```

### Step 3: Wait and Test
```bash
# Wait 5-60 minutes for DNS propagation
curl -I https://agents.trucks.ai
```

## 📋 Detailed Setup Process

### Prerequisites

#### 1. Domain Ownership Verification
Since `trucks.ai` validation is already in place, we need to ensure the subdomain is properly configured.

#### 2. Google Cloud Setup
- Cloud Run service deployed ✅
- Appropriate IAM permissions ✅
- Domain verification (will be handled)

#### 3. Cloudflare Access
- Access to trucks.ai domain in Cloudflare
- DNS management permissions

### Step-by-Step Configuration

#### Step 1: Verify Domain in Google Cloud
```bash
# Check if trucks.ai is already verified
gcloud domains list-user-verified

# If not verified, add domain verification
# Go to: https://console.cloud.google.com/apis/credentials/domainverification
```

#### Step 2: Create Domain Mapping
```bash
# Create the domain mapping
gcloud run domain-mappings create \
  --service=adk-analyst-jenkins-agent-production \
  --domain=agents.trucks.ai \
  --region=us-central1
```

#### Step 3: Get DNS Configuration
```bash
# Get the CNAME target
gcloud run domain-mappings describe agents.trucks.ai \
  --region=us-central1 \
  --format="value(status.resourceRecords[0].rrdata)"
```

#### Step 4: Configure Cloudflare DNS
1. **Login to Cloudflare Dashboard**
2. **Select trucks.ai domain**
3. **Go to DNS settings**
4. **Add CNAME record**:
   - **Type**: CNAME
   - **Name**: agents
   - **Target**: [CNAME target from Step 3]
   - **TTL**: Auto (or 300 seconds)
   - **Proxy Status**: DNS only (gray cloud)

## 🔧 Manual Configuration

### Google Cloud Console Method

#### 1. Domain Mapping via Console
1. Go to: https://console.cloud.google.com/run
2. Select `adk-analyst-jenkins-agent-production` service
3. Click "Manage Custom Domains"
4. Click "Add Mapping"
5. Enter domain: `agents.trucks.ai`
6. Click "Continue"

#### 2. Get DNS Records
1. After mapping creation, note the CNAME target
2. Copy the provided DNS configuration

### Cloudflare Configuration

#### DNS Record Configuration
```
Record Type: CNAME
Name: agents
Target: ghs.googlehosted.com (example - use actual target)
TTL: Auto
Proxy: DNS only (important!)
```

#### SSL/TLS Settings
1. **Go to SSL/TLS tab**
2. **Set encryption mode**: Full (strict) or Flexible
3. **Enable**: Always Use HTTPS
4. **Edge Certificates**: Let Google manage SSL

## 🔐 SSL Certificate Setup

### Automatic SSL Provisioning
Google Cloud Run automatically provisions SSL certificates for custom domains:

- **Certificate Type**: Google-managed
- **Provisioning Time**: 5-60 minutes after DNS propagation
- **Renewal**: Automatic
- **Protocols**: TLS 1.2, TLS 1.3

### SSL Verification
```bash
# Check SSL certificate
openssl s_client -connect agents.trucks.ai:443 -servername agents.trucks.ai

# Check certificate details
curl -vI https://agents.trucks.ai
```

## 🧪 Testing and Verification

### DNS Propagation Check
```bash
# Check DNS resolution
dig agents.trucks.ai
nslookup agents.trucks.ai

# Check from different locations
dig @******* agents.trucks.ai
dig @******* agents.trucks.ai
```

### Service Connectivity
```bash
# Test HTTP redirect
curl -I http://agents.trucks.ai

# Test HTTPS
curl -I https://agents.trucks.ai

# Test full response
curl https://agents.trucks.ai
```

### Domain Mapping Status
```bash
# Check mapping status
gcloud run domain-mappings describe agents.trucks.ai --region=us-central1

# List all mappings
gcloud run domain-mappings list --region=us-central1
```

## 🔍 Troubleshooting

### Common Issues

#### 1. DNS Not Resolving
```bash
# Check DNS propagation
dig agents.trucks.ai

# Verify CNAME record
dig CNAME agents.trucks.ai
```

**Solutions**:
- Verify CNAME record in Cloudflare
- Wait for DNS propagation (up to 48 hours)
- Check TTL settings

#### 2. SSL Certificate Issues
```bash
# Check certificate status
curl -vI https://agents.trucks.ai
```

**Solutions**:
- Wait for automatic provisioning (5-60 minutes)
- Verify DNS is resolving correctly
- Check domain mapping status

#### 3. 404 or Service Unavailable
```bash
# Check service status
gcloud run services describe adk-analyst-jenkins-agent-production --region=us-central1
```

**Solutions**:
- Verify service is running
- Check service logs
- Verify domain mapping

#### 4. Domain Verification Issues
**Solutions**:
- Verify trucks.ai domain ownership in Google Cloud Console
- Add domain verification if needed
- Check IAM permissions

### Debug Commands
```bash
# Check domain mapping
gcloud run domain-mappings describe agents.trucks.ai --region=us-central1

# Check service status
gcloud run services describe adk-analyst-jenkins-agent-production --region=us-central1

# View service logs
gcloud run services logs read adk-analyst-jenkins-agent-production --region=us-central1

# Test connectivity
curl -v https://agents.trucks.ai
```

## 📊 Expected Results

### After Successful Setup
1. **DNS Resolution**: `agents.trucks.ai` resolves to Google Cloud Run
2. **HTTPS Access**: https://agents.trucks.ai loads the ADK Analyst interface
3. **SSL Certificate**: Valid Google-managed certificate
4. **Redirect**: HTTP automatically redirects to HTTPS

### Performance Expectations
- **DNS Resolution**: < 100ms
- **SSL Handshake**: < 500ms
- **First Byte**: < 1000ms
- **Page Load**: < 3000ms

## 🔄 Maintenance

### Regular Tasks
- **Monitor SSL expiry**: Automatic renewal by Google
- **Check DNS health**: Periodic resolution tests
- **Update documentation**: Keep records current

### Updates and Changes
```bash
# Update domain mapping
gcloud run domain-mappings update agents.trucks.ai --region=us-central1

# Delete domain mapping (if needed)
gcloud run domain-mappings delete agents.trucks.ai --region=us-central1
```

## 📚 Additional Resources

### Google Cloud Documentation
- [Cloud Run Custom Domains](https://cloud.google.com/run/docs/mapping-custom-domains)
- [Domain Verification](https://cloud.google.com/endpoints/docs/openapi/verify-domain-name)
- [SSL Certificates](https://cloud.google.com/run/docs/securing/using-https)

### Cloudflare Documentation
- [CNAME Records](https://developers.cloudflare.com/dns/manage-dns-records/reference/dns-record-types/#cname)
- [DNS Management](https://developers.cloudflare.com/dns/)
- [SSL/TLS Settings](https://developers.cloudflare.com/ssl/)

## 🎯 Quick Reference

### Essential Commands
```bash
# Setup domain
./gcp-deployment/setup-custom-domain.sh agents.trucks.ai

# Check status
gcloud run domain-mappings describe agents.trucks.ai --region=us-central1

# Test connectivity
curl -I https://agents.trucks.ai

# View logs
gcloud run services logs read adk-analyst-jenkins-agent-production --region=us-central1
```

### Cloudflare DNS Record
```
Type: CNAME
Name: agents
Target: [from Google Cloud domain mapping]
TTL: Auto
Proxy: DNS only
```

---

**Ready to set up your custom domain!** 🚀

Run the setup script and follow the Cloudflare configuration steps to get `agents.trucks.ai` pointing to your ADK Analyst service.
