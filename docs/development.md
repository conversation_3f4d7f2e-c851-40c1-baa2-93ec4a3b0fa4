# Development Guide - ADK Analyst Jenkins Reader Agent

This guide covers development setup, coding standards, testing, and contribution guidelines for the Jenkins Reader Agent.

## Development Environment Setup

### Prerequisites

- Python 3.12+
- Git
- Docker
- Google Cloud SDK (optional, for cloud testing)

### Local Development Setup

1. **Clone and Setup**
```bash
<NAME_EMAIL>:truxt-ai/adk-analyst.git
cd adk-analyst
```

2. **Virtual Environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install Dependencies**
```bash
# Production dependencies
pip install -r requirements.txt

# Development dependencies
pip install pytest black isort mypy flake8 pre-commit
```

4. **Pre-commit Hooks**
```bash
pre-commit install
```

## Project Structure

```
adk-analyst/
├── jenkins_agent/          # Main agent package
│   ├── __init__.py
│   ├── agent.py            # Main agent definition
│   ├── tools/              # Jenkins tools
│   ├── security/           # Security modules
│   └── utils/              # Utility functions
├── docs/                   # Documentation
├── tests/                  # Test suite
├── scripts/                # Utility scripts
├── examples/               # Usage examples
├── .github/                # GitHub workflows
├── adk.yaml               # ADK configuration
├── Dockerfile.adk         # Production Docker image
├── docker-compose.yml     # Development environment
├── requirements.txt       # Python dependencies
├── .env.example          # Environment template
├── .gitignore            # Git ignore rules
└── README.md             # Main documentation
```

## Coding Standards

### Python Style Guide

We follow PEP 8 with some modifications:

- **Line Length**: 88 characters (Black default)
- **Imports**: Use isort for import organization
- **Type Hints**: Required for all public functions
- **Docstrings**: Google style docstrings

### Code Formatting

```bash
# Format code
black jenkins_agent/ tests/

# Sort imports
isort jenkins_agent/ tests/

# Lint code
flake8 jenkins_agent/ tests/

# Type checking
mypy jenkins_agent/
```

### Example Code Style

```python
"""Module docstring describing the purpose."""

from typing import Dict, List, Optional
import logging

from google.adk import Agent
from jenkins_agent.utils import validate_input


logger = logging.getLogger(__name__)


class JenkinsAnalyzer:
    """Jenkins data analyzer with AI capabilities.
    
    This class provides methods for analyzing Jenkins data
    using Google's ADK framework.
    """
    
    def __init__(self, config: Dict[str, str]) -> None:
        """Initialize the analyzer.
        
        Args:
            config: Configuration dictionary containing API keys and settings.
        """
        self.config = config
        self._client = None
    
    def analyze_jobs(
        self, 
        jobs: List[Dict[str, str]], 
        filters: Optional[Dict[str, str]] = None
    ) -> Dict[str, List[str]]:
        """Analyze Jenkins jobs and return insights.
        
        Args:
            jobs: List of Jenkins job dictionaries.
            filters: Optional filters to apply to the analysis.
            
        Returns:
            Dictionary containing analysis results and recommendations.
            
        Raises:
            ValueError: If jobs list is empty or invalid.
        """
        if not jobs:
            raise ValueError("Jobs list cannot be empty")
        
        # Implementation here
        return {"insights": [], "recommendations": []}
```

## Testing

### Test Structure

```
tests/
├── unit/                   # Unit tests
│   ├── test_agent.py
│   ├── test_tools.py
│   └── test_security.py
├── integration/            # Integration tests
│   ├── test_jenkins_api.py
│   └── test_adk_integration.py
├── e2e/                   # End-to-end tests
│   └── test_full_workflow.py
├── fixtures/              # Test data
│   ├── jenkins_data.json
│   └── mock_responses.py
└── conftest.py           # Pytest configuration
```

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=jenkins_agent --cov-report=html

# Run specific test categories
pytest tests/unit/
pytest tests/integration/
pytest tests/e2e/

# Run tests with verbose output
pytest -v

# Run tests matching a pattern
pytest -k "test_jenkins"
```

### Writing Tests

```python
"""Test module for Jenkins agent functionality."""

import pytest
from unittest.mock import Mock, patch

from jenkins_agent.agent import JenkinsAgent
from jenkins_agent.tools import validate_jenkins_connection


class TestJenkinsAgent:
    """Test cases for JenkinsAgent class."""
    
    @pytest.fixture
    def agent(self):
        """Create a test agent instance."""
        return JenkinsAgent(config={"test": True})
    
    def test_agent_initialization(self, agent):
        """Test agent initializes correctly."""
        assert agent is not None
        assert agent.config["test"] is True
    
    @patch('jenkins_agent.tools.requests.get')
    def test_validate_connection_success(self, mock_get):
        """Test successful Jenkins connection validation."""
        mock_get.return_value.status_code = 200
        mock_get.return_value.json.return_value = {"version": "2.401.3"}
        
        result = validate_jenkins_connection("https://jenkins.example.com")
        
        assert result["status"] == "success"
        assert "version" in result
    
    def test_validate_connection_invalid_url(self):
        """Test connection validation with invalid URL."""
        with pytest.raises(ValueError):
            validate_jenkins_connection("invalid-url")
```

### Test Data and Fixtures

```python
# tests/fixtures/jenkins_data.py
"""Test data fixtures for Jenkins API responses."""

SAMPLE_JOBS = [
    {
        "name": "build-app",
        "url": "https://jenkins.example.com/job/build-app/",
        "color": "blue",
        "buildable": True
    },
    {
        "name": "deploy-prod",
        "url": "https://jenkins.example.com/job/deploy-prod/",
        "color": "red",
        "buildable": True
    }
]

SAMPLE_BUILD_HISTORY = [
    {
        "number": 123,
        "result": "SUCCESS",
        "timestamp": 1640995200000,
        "duration": 300000
    },
    {
        "number": 122,
        "result": "FAILURE",
        "timestamp": 1640991600000,
        "duration": 150000
    }
]
```

## Docker Development

### Development Container

```bash
# Build development image
docker build -f Dockerfile.adk -t jenkins-agent-dev .

# Run development container
docker run -it --rm \
  -v $(pwd):/app \
  -p 8000:8000 \
  jenkins-agent-dev bash

# Run tests in container
docker run --rm \
  -v $(pwd):/app \
  jenkins-agent-dev pytest
```

### Docker Compose for Development

```bash
# Start development environment
docker-compose up -d

# View logs
docker-compose logs -f

# Run tests
docker-compose exec app pytest

# Stop environment
docker-compose down
```

## Debugging

### Local Debugging

```python
# Add to your code for debugging
import pdb; pdb.set_trace()

# Or use ipdb for better experience
import ipdb; ipdb.set_trace()
```

### Logging Configuration

```python
import logging

# Configure logging for development
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)
logger.debug("Debug message")
```

### Environment Variables for Debugging

```bash
# Enable debug mode
export DEBUG=true
export LOG_LEVEL=DEBUG

# Mock external services
export MOCK_JENKINS=true
export MOCK_GOOGLE_CLOUD=true
```

## Contributing

### Workflow

1. **Create Feature Branch**
```bash
git checkout -b feature/your-feature-name
```

2. **Make Changes**
- Write code following style guidelines
- Add tests for new functionality
- Update documentation as needed

3. **Run Quality Checks**
```bash
# Format and lint
black jenkins_agent/ tests/
isort jenkins_agent/ tests/
flake8 jenkins_agent/ tests/
mypy jenkins_agent/

# Run tests
pytest --cov=jenkins_agent
```

4. **Commit Changes**
```bash
git add .
git commit -m "feat: add new Jenkins analysis feature"
```

5. **Push and Create PR**
```bash
git push origin feature/your-feature-name
# Create pull request on GitHub
```

### Commit Message Format

We use conventional commits:

- `feat:` New feature
- `fix:` Bug fix
- `docs:` Documentation changes
- `style:` Code style changes
- `refactor:` Code refactoring
- `test:` Test additions or changes
- `chore:` Maintenance tasks

### Code Review Guidelines

- All code must be reviewed before merging
- Tests must pass and coverage should not decrease
- Documentation must be updated for new features
- Security implications must be considered

## Release Process

### Version Management

We use semantic versioning (SemVer):
- `MAJOR.MINOR.PATCH`
- Major: Breaking changes
- Minor: New features (backward compatible)
- Patch: Bug fixes

### Release Steps

1. **Update Version**
```bash
# Update version in adk.yaml and __init__.py
git commit -m "chore: bump version to 1.2.0"
```

2. **Create Release Tag**
```bash
git tag -a v1.2.0 -m "Release version 1.2.0"
git push origin v1.2.0
```

3. **Build and Push Docker Image**
```bash
docker build -f Dockerfile.adk -t jenkins-agent:1.2.0 .
docker tag jenkins-agent:1.2.0 jenkins-agent:latest
# Push to registry
```

4. **Create GitHub Release**
- Use GitHub UI to create release from tag
- Include changelog and migration notes

## Performance Optimization

### Profiling

```python
# Profile code performance
import cProfile
import pstats

profiler = cProfile.Profile()
profiler.enable()

# Your code here

profiler.disable()
stats = pstats.Stats(profiler)
stats.sort_stats('cumulative')
stats.print_stats()
```

### Memory Usage

```python
# Monitor memory usage
import tracemalloc

tracemalloc.start()

# Your code here

current, peak = tracemalloc.get_traced_memory()
print(f"Current memory usage: {current / 1024 / 1024:.1f} MB")
print(f"Peak memory usage: {peak / 1024 / 1024:.1f} MB")
tracemalloc.stop()
```

### Load Testing

```bash
# Install load testing tools
pip install locust

# Run load tests
locust -f tests/load/locustfile.py --host=http://localhost:8000
```

## Security

### Security Testing

```bash
# Run security scans
bandit -r jenkins_agent/

# Check for known vulnerabilities
safety check

# Scan Docker images
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy jenkins-agent:latest
```

### Secrets Management

- Never commit secrets to version control
- Use environment variables for configuration
- Rotate credentials regularly
- Use Google Secret Manager for production

## Troubleshooting

### Common Development Issues

1. **Import Errors**: Ensure virtual environment is activated
2. **Test Failures**: Check test data and mock configurations
3. **Docker Issues**: Verify Docker daemon is running
4. **Authentication**: Check Google Cloud credentials

### Getting Help

- Check existing issues on GitHub
- Review documentation in `docs/`
- Ask questions in team chat
- Create detailed bug reports with reproduction steps

---

For more information, see:
- [Setup Guide](setup.md)
- [Design Overview](design.md)
- [Test Results](../TEST_RESULTS.md)
