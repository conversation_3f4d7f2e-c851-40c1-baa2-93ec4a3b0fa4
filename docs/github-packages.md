# GitHub Packages Deployment Guide

This guide explains how to push the ADK Analyst Docker image to GitHub Container Registry (GitHub Packages).

## 🎯 Quick Start

### Option 1: Using the Automated Script
```bash
# Run the automated push script
./scripts/push-to-github-packages.sh

# Or with a GitHub token
./scripts/push-to-github-packages.sh ghp_your_token_here
```

### Option 2: Manual Steps
```bash
# 1. Authenticate with GitHub Container Registry
echo $GITHUB_TOKEN | docker login ghcr.io -u USERNAME --password-stdin

# 2. Tag the image
docker tag adk-analyst:latest ghcr.io/truxt-ai/adk-analyst:latest

# 3. Push to GitHub Packages
docker push ghcr.io/truxt-ai/adk-analyst:latest
```

## 📋 Prerequisites

### 1. GitHub Personal Access Token
Create a Personal Access Token with the following scopes:
- `packages:write` - Required to push packages
- `repo` - Required if the repository is private

**Create token at**: https://github.com/settings/tokens/new

### 2. Docker Image
Ensure you have the ADK Analyst image built:
```bash
# Build if not already built
docker build -f Dockerfile.adk -t ghcr.io/truxt-ai/adk-analyst:latest .

# Verify image exists
docker images | grep adk-analyst
```

### 3. Repository Access
Ensure you have push access to the `truxt-ai/adk-analyst` repository.

## 🔐 Authentication Methods

### Method 1: Environment Variable
```bash
export GITHUB_TOKEN=ghp_your_token_here
./scripts/push-to-github-packages.sh
```

### Method 2: Direct Parameter
```bash
./scripts/push-to-github-packages.sh ghp_your_token_here
```

### Method 3: Interactive Prompt
```bash
# Script will prompt for token if not provided
./scripts/push-to-github-packages.sh
```

### Method 4: Manual Docker Login
```bash
echo ghp_your_token_here | docker login ghcr.io -u your-github-username --password-stdin
```

## 🏷️ Image Tagging Strategy

The script automatically creates multiple tags:

### Version Tags
- `latest` - Latest stable version
- `v1.0.0` - Semantic version
- `20250602` - Date-based version (YYYYMMDD)
- `commit-abc123` - Git commit SHA

### Custom Tags
```bash
# Add custom tags before pushing
docker tag ghcr.io/truxt-ai/adk-analyst:latest ghcr.io/truxt-ai/adk-analyst:production
docker tag ghcr.io/truxt-ai/adk-analyst:latest ghcr.io/truxt-ai/adk-analyst:stable
```

## 📦 Package Configuration

### Package Visibility
After pushing, configure package settings:

1. **Go to**: https://github.com/orgs/truxt-ai/packages/container/package/adk-analyst/settings
2. **Set visibility**: Public or Private
3. **Add description**: "ADK Analyst Jenkins Reader Agent - AI-powered Jenkins analysis"
4. **Configure access**: Set team/user permissions

### Package Metadata
```yaml
# Add labels to Dockerfile for better metadata
LABEL org.opencontainers.image.title="ADK Analyst Jenkins Reader Agent"
LABEL org.opencontainers.image.description="Enterprise-grade Jenkins read-only agent with AI analysis"
LABEL org.opencontainers.image.vendor="Truxt AI"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.source="https://github.com/truxt-ai/adk-analyst"
```

## 🚀 Deployment Commands

### Push All Tags
```bash
# Push all available tags
docker images ghcr.io/truxt-ai/adk-analyst --format "{{.Tag}}" | xargs -I {} docker push ghcr.io/truxt-ai/adk-analyst:{}
```

### Push Specific Version
```bash
# Push specific version
docker push ghcr.io/truxt-ai/adk-analyst:v1.0.0
```

### Verify Push
```bash
# List remote tags (requires GitHub CLI)
gh api repos/truxt-ai/adk-analyst/packages/container/adk-analyst/versions

# Or check the web interface
open https://github.com/orgs/truxt-ai/packages/container/package/adk-analyst
```

## 📥 Pulling from GitHub Packages

### Public Package
```bash
# Pull latest version
docker pull ghcr.io/truxt-ai/adk-analyst:latest

# Pull specific version
docker pull ghcr.io/truxt-ai/adk-analyst:v1.0.0
```

### Private Package
```bash
# Authenticate first
echo $GITHUB_TOKEN | docker login ghcr.io -u USERNAME --password-stdin

# Then pull
docker pull ghcr.io/truxt-ai/adk-analyst:latest
```

## 🔄 CI/CD Integration

### GitHub Actions
```yaml
name: Build and Push to GitHub Packages

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Log in to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ghcr.io/truxt-ai/adk-analyst
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}

    - name: Build and push
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.adk
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
```

## 🛠️ Management Commands

### List Package Versions
```bash
# Using GitHub CLI
gh api repos/truxt-ai/adk-analyst/packages/container/adk-analyst/versions \
  --jq '.[] | {id: .id, name: .name, created_at: .created_at}'
```

### Delete Package Version
```bash
# Delete specific version (be careful!)
gh api --method DELETE repos/truxt-ai/adk-analyst/packages/container/adk-analyst/versions/VERSION_ID
```

### Update Package Settings
```bash
# Make package public
gh api --method PATCH repos/truxt-ai/adk-analyst/packages/container/adk-analyst \
  --field visibility=public
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Authentication Failed
```bash
# Error: unauthorized: authentication required
# Solution: Check token permissions and expiry
echo $GITHUB_TOKEN | docker login ghcr.io -u USERNAME --password-stdin
```

#### 2. Package Not Found
```bash
# Error: repository does not exist
# Solution: Ensure repository exists and you have access
# Check: https://github.com/truxt-ai/adk-analyst
```

#### 3. Permission Denied
```bash
# Error: insufficient_scope
# Solution: Token needs 'packages:write' scope
# Create new token: https://github.com/settings/tokens/new
```

#### 4. Image Too Large
```bash
# Error: blob upload unknown
# Solution: Optimize image size
docker build --squash -f Dockerfile.adk -t ghcr.io/truxt-ai/adk-analyst:latest .
```

### Debug Commands
```bash
# Check Docker login status
docker system info | grep -A 10 "Registry Mirrors"

# Verify image exists locally
docker images ghcr.io/truxt-ai/adk-analyst

# Test authentication
docker pull hello-world
```

## 📊 Package Analytics

### View Package Statistics
- **Downloads**: Track pull statistics
- **Versions**: Monitor version usage
- **Storage**: Monitor package size

Access at: https://github.com/orgs/truxt-ai/packages/container/package/adk-analyst/insights

## 🔒 Security Best Practices

### Token Security
- Use tokens with minimal required scopes
- Set token expiration dates
- Rotate tokens regularly
- Store tokens securely (environment variables, secrets)

### Package Security
- Scan images for vulnerabilities
- Use minimal base images
- Keep dependencies updated
- Enable security alerts

### Access Control
- Use organization-level access controls
- Implement team-based permissions
- Regular access reviews
- Audit package access logs

## 📚 Additional Resources

- [GitHub Packages Documentation](https://docs.github.com/en/packages)
- [Container Registry Guide](https://docs.github.com/en/packages/working-with-a-github-packages-registry/working-with-the-container-registry)
- [Docker Login Documentation](https://docs.docker.com/engine/reference/commandline/login/)
- [GitHub CLI Packages](https://cli.github.com/manual/gh_api)

## 🎯 Quick Reference

### Essential Commands
```bash
# Build and tag
docker build -f Dockerfile.adk -t ghcr.io/truxt-ai/adk-analyst:latest .

# Authenticate
echo $GITHUB_TOKEN | docker login ghcr.io -u USERNAME --password-stdin

# Push
docker push ghcr.io/truxt-ai/adk-analyst:latest

# Pull
docker pull ghcr.io/truxt-ai/adk-analyst:latest

# Run
docker run -p 8000:8000 ghcr.io/truxt-ai/adk-analyst:latest
```

### Package URLs
- **Package Page**: https://github.com/orgs/truxt-ai/packages/container/package/adk-analyst
- **Organization Packages**: https://github.com/orgs/truxt-ai/packages
- **Settings**: https://github.com/orgs/truxt-ai/packages/container/package/adk-analyst/settings

---

**Ready to push your Docker image to GitHub Packages!** 🚀
