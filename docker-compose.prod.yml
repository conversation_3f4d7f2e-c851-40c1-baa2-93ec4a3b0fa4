version: '3.8'

services:
  jenkins-reader-agent:
    image: jenkins-reader-agent:latest
    container_name: jenkins-reader-agent-prod
    ports:
      - "8000:8000"
    environment:
      - GOOGLE_GENAI_USE_VERTEXAI=1
      - GOOGLE_CLOUD_PROJECT=truxtsaas
      - GOOGLE_CLOUD_LOCATION=us-central1
      - GOOGLE_APPLICATION_CREDENTIALS=/run/secrets/gcp_service_account
      - JENKINS_CREDENTIALS_SECRET=jenkins-credentials
      - ALLOWED_JENKINS_DOMAINS=jenkins.truxt.ai
      - LOG_LEVEL=INFO
      - ENABLE_AUDIT_LOGGING=true
      - ENABLE_RATE_LIMITING=true
      - DEBUG=false
      - MAX_CONCURRENT_REQUESTS=10
      - RATE_LIMIT_PER_MINUTE=100
    secrets:
      - gcp_service_account
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - jenkins-agent-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

networks:
  jenkins-agent-network:
    driver: bridge

secrets:
  gcp_service_account:
    file: ./service-account-key.json
