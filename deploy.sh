#!/bin/bash

# Jenkins Reader Agent Deployment Script
# This script builds and deploys the Jenkins Reader Agent with ADK Web

set -e

echo "🚀 Jenkins Reader Agent Deployment Script"
echo "=========================================="

# Configuration
IMAGE_NAME="jenkins-reader-agent"
TAG="latest"
CONTAINER_NAME="jenkins-reader-agent-prod"
PORT="8000"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_status "Docker is running"

# Check if service account key exists
if [ ! -f "jenkins-reader-agent/service-account-key.json" ]; then
    print_error "Service account key not found at jenkins-reader-agent/service-account-key.json"
    print_warning "Please ensure you have downloaded the service account key:"
    echo "gcloud iam service-accounts keys create jenkins-reader-agent/service-account-key.json --iam-account=<EMAIL>"
    exit 1
fi

print_status "Service account key found"

# Stop existing container if running
if docker ps -q -f name=$CONTAINER_NAME | grep -q .; then
    print_warning "Stopping existing container: $CONTAINER_NAME"
    docker stop $CONTAINER_NAME
    docker rm $CONTAINER_NAME
fi

# Build the Docker image
print_status "Building Docker image: $IMAGE_NAME:$TAG"
docker build -f Dockerfile.adk -t $IMAGE_NAME:$TAG .

if [ $? -eq 0 ]; then
    print_status "Docker image built successfully"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# Run the container
print_status "Starting container: $CONTAINER_NAME"
docker run -d \
    --name $CONTAINER_NAME \
    -p $PORT:8000 \
    --restart unless-stopped \
    --health-cmd="curl -f http://localhost:8000/health || exit 1" \
    --health-interval=30s \
    --health-timeout=10s \
    --health-retries=3 \
    --health-start-period=40s \
    $IMAGE_NAME:$TAG

if [ $? -eq 0 ]; then
    print_status "Container started successfully"
    echo ""
    echo "🌐 Jenkins Reader Agent is now running!"
    echo "📍 Access the web interface at: http://localhost:$PORT"
    echo "🔍 Check container status: docker ps"
    echo "📋 View logs: docker logs $CONTAINER_NAME"
    echo "🛑 Stop container: docker stop $CONTAINER_NAME"
    echo ""
    print_status "Deployment completed successfully! 🎉"
else
    print_error "Failed to start container"
    exit 1
fi
