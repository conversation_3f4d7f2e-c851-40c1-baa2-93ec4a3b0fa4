# 🌐 Cloudflare DNS Setup for agents.truxt.ai

## ✅ **GOOGLE CLOUD DOMAIN MAPPING CREATED**

The domain mapping has been successfully created in Google Cloud Run. Now we need to configure the DNS record in Cloudflare.

---

## 📋 **DNS CONFIGURATION REQUIRED**

### **CNAME Record Details**
```
Type: CNAME
Name: agents
Target: ghs.googlehosted.com
TTL: Auto (or 300 seconds)
Proxy Status: DNS only (gray cloud)
```

### **Full Domain Configuration**
- **Full Domain**: `agents.truxt.ai`
- **Subdomain**: `agents`
- **Target**: `ghs.googlehosted.com`
- **Service**: ADK Analyst Jenkins Reader Agent

---

## 🚀 **CLOUDFLARE SETUP STEPS**

### **Step 1: Access Cloudflare Dashboard**
1. Login to Cloudflare Dashboard
2. Select the `truxt.ai` domain
3. Navigate to **DNS** section

### **Step 2: Add CNAME Record**
1. Click **"Add record"**
2. Configure the record:
   - **Type**: `CNAME`
   - **Name**: `agents`
   - **Target**: `ghs.googlehosted.com`
   - **TTL**: `Auto` (or `300` seconds)
   - **Proxy status**: `DNS only` (gray cloud icon)

### **Step 3: Save Configuration**
1. Click **"Save"**
2. Wait for DNS propagation (5-60 minutes)

---

## ⚙️ **DETAILED CONFIGURATION**

### **DNS Record Configuration**
```
Record Type: CNAME
Name: agents
Content: ghs.googlehosted.com
TTL: Auto
Proxy: DNS only (IMPORTANT: Must be gray cloud, not orange)
```

### **Why DNS Only (Gray Cloud)?**
- **SSL Certificate**: Google Cloud Run manages SSL certificates
- **Direct Connection**: Required for proper certificate provisioning
- **Performance**: Avoids double-proxying issues

### **SSL/TLS Settings**
Since we're using DNS only, Cloudflare won't proxy the traffic, but you can still configure:
- **Encryption Mode**: Full (strict) - recommended
- **Always Use HTTPS**: Enabled
- **HSTS**: Can be enabled for additional security

---

## 🧪 **VERIFICATION STEPS**

### **1. DNS Propagation Check**
```bash
# Check DNS resolution
dig agents.truxt.ai
nslookup agents.truxt.ai

# Check CNAME specifically
dig CNAME agents.truxt.ai

# Check from different DNS servers
dig @******* agents.truxt.ai
dig @******* agents.truxt.ai
```

### **2. SSL Certificate Provisioning**
After DNS propagation, Google will automatically provision an SSL certificate:
```bash
# Check SSL certificate (after 5-60 minutes)
openssl s_client -connect agents.truxt.ai:443 -servername agents.truxt.ai

# Test HTTPS connectivity
curl -I https://agents.truxt.ai
```

### **3. Service Accessibility**
```bash
# Test HTTP (should redirect to HTTPS)
curl -I http://agents.truxt.ai

# Test HTTPS
curl -I https://agents.truxt.ai

# Full test
curl https://agents.truxt.ai
```

---

## 📊 **EXPECTED TIMELINE**

### **DNS Propagation**
- **Cloudflare**: 1-5 minutes
- **Global DNS**: 5-60 minutes
- **Full Propagation**: Up to 24 hours (typically much faster)

### **SSL Certificate Provisioning**
- **Start**: After DNS resolves correctly
- **Duration**: 5-60 minutes
- **Status**: Automatic by Google Cloud Run

### **Service Availability**
- **HTTP**: Immediate after DNS propagation
- **HTTPS**: After SSL certificate provisioning
- **Full Functionality**: Complete setup in 1-2 hours

---

## 🔍 **TROUBLESHOOTING**

### **Common Issues**

#### **1. DNS Not Resolving**
```bash
# Check current DNS
dig agents.truxt.ai

# If not resolving:
# - Verify CNAME record in Cloudflare
# - Check TTL settings
# - Wait for propagation
```

#### **2. SSL Certificate Not Provisioning**
```bash
# Check domain mapping status
gcloud beta run domain-mappings list --region=us-central1

# If certificate not provisioning:
# - Ensure DNS is resolving to ghs.googlehosted.com
# - Verify proxy status is "DNS only" (gray cloud)
# - Wait up to 60 minutes
```

#### **3. 404 or Service Unavailable**
```bash
# Check service status
gcloud run services describe adk-analyst-jenkins-agent-production --region=us-central1

# If service issues:
# - Verify service is running
# - Check service logs
# - Verify domain mapping
```

### **Debug Commands**
```bash
# Check domain mapping
gcloud beta run domain-mappings list --region=us-central1

# Check service status
gcloud run services describe adk-analyst-jenkins-agent-production --region=us-central1

# View service logs
gcloud run services logs read adk-analyst-jenkins-agent-production --region=us-central1

# Test connectivity
curl -v https://agents.truxt.ai
```

---

## 📱 **CLOUDFLARE MOBILE APP**

You can also configure this using the Cloudflare mobile app:
1. Open Cloudflare app
2. Select `truxt.ai` domain
3. Go to DNS records
4. Add CNAME record with the same configuration

---

## 🎯 **VERIFICATION CHECKLIST**

### **Before DNS Configuration**
- ✅ Google Cloud domain mapping created
- ✅ Service running on Cloud Run
- ✅ Domain target obtained: `ghs.googlehosted.com`

### **After DNS Configuration**
- ⏳ CNAME record added in Cloudflare
- ⏳ DNS propagation (5-60 minutes)
- ⏳ SSL certificate provisioning (5-60 minutes)
- ⏳ Service accessible via HTTPS

### **Final Verification**
```bash
# These should all work after setup:
curl -I https://agents.truxt.ai
dig agents.truxt.ai
nslookup agents.truxt.ai
```

---

## 🌐 **FINAL RESULT**

### **After Successful Setup**
- **URL**: https://agents.truxt.ai
- **Service**: ADK Analyst Jenkins Reader Agent
- **SSL**: Google-managed certificate
- **Performance**: Direct connection to Google Cloud Run

### **Service Features Available**
- **Web Interface**: Interactive ADK Analyst UI
- **Jenkins Analysis**: 6 specialized Jenkins tools
- **AI-Powered Insights**: Vertex AI integration
- **Secure Access**: HTTPS with modern TLS

---

## 📞 **QUICK REFERENCE**

### **Cloudflare DNS Record**
```
Type: CNAME
Name: agents
Target: ghs.googlehosted.com
TTL: Auto
Proxy: DNS only (gray cloud)
```

### **Verification Commands**
```bash
# DNS check
dig agents.truxt.ai

# SSL check
curl -I https://agents.truxt.ai

# Service check
curl https://agents.truxt.ai
```

### **Google Cloud Status**
```bash
# Check domain mapping
gcloud beta run domain-mappings list --region=us-central1

# Check service
gcloud run services describe adk-analyst-jenkins-agent-production --region=us-central1
```

---

## 🎉 **NEXT STEPS**

1. **Add CNAME record in Cloudflare** (as described above)
2. **Wait for DNS propagation** (5-60 minutes)
3. **Test the domain**: https://agents.truxt.ai
4. **Verify SSL certificate** is automatically provisioned
5. **Access the ADK Analyst interface**

---

**🚀 Ready to configure Cloudflare DNS!**

The Google Cloud side is complete. Now just add the CNAME record in Cloudflare and wait for propagation.
