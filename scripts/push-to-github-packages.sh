#!/bin/bash

# Push Docker Image to GitHub Container Registry (GitHub Packages)
# Usage: ./scripts/push-to-github-packages.sh [github-token]

set -e

# Configuration
GITHUB_ORG="truxt-ai"
REPO_NAME="adk-analyst"
IMAGE_NAME="adk-analyst"
REGISTRY="ghcr.io"
FULL_IMAGE_NAME="${REGISTRY}/${GITHUB_ORG}/${IMAGE_NAME}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

show_usage() {
    echo "Usage: $0 [github-token]"
    echo ""
    echo "This script pushes the ADK Analyst Docker image to GitHub Container Registry."
    echo ""
    echo "Options:"
    echo "  github-token    GitHub Personal Access Token with packages:write scope"
    echo ""
    echo "If no token is provided, the script will prompt for it or use GITHUB_TOKEN env var."
    echo ""
    echo "Examples:"
    echo "  $0 ghp_xxxxxxxxxxxxxxxxxxxx"
    echo "  GITHUB_TOKEN=ghp_xxxxxxxxxxxxxxxxxxxx $0"
    echo ""
    echo "GitHub Packages URL: https://github.com/orgs/truxt-ai/packages"
}

check_prerequisites() {
    log_step "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running"
        exit 1
    fi
    
    # Check if image exists
    if ! docker images "${FULL_IMAGE_NAME}:latest" --format "{{.Repository}}" | grep -q "${FULL_IMAGE_NAME}"; then
        log_warning "Image ${FULL_IMAGE_NAME}:latest not found locally"
        log_info "Available images:"
        docker images | grep -E "(adk-analyst|truxt-ai)" || echo "No ADK Analyst images found"
        
        # Try to build the image
        log_info "Building the image..."
        if [[ -f "Dockerfile.adk" ]]; then
            docker build -f Dockerfile.adk -t "${FULL_IMAGE_NAME}:latest" .
            log_success "Image built successfully"
        else
            log_error "Dockerfile.adk not found. Please run from project root."
            exit 1
        fi
    else
        log_success "Image ${FULL_IMAGE_NAME}:latest found"
    fi
    
    log_success "Prerequisites check completed"
}

get_github_token() {
    local token=${1:-}
    
    # Try to get token from parameter, environment, or prompt
    if [[ -n "$token" ]]; then
        echo "$token"
    elif [[ -n "$GITHUB_TOKEN" ]]; then
        echo "$GITHUB_TOKEN"
    else
        log_warning "GitHub token not provided"
        echo ""
        echo "To push to GitHub Container Registry, you need a Personal Access Token with 'packages:write' scope."
        echo ""
        echo "Create one at: https://github.com/settings/tokens/new"
        echo "Required scopes: packages:write, repo (if private repo)"
        echo ""
        read -p "Enter your GitHub token: " -s token
        echo ""
        echo "$token"
    fi
}

authenticate_github() {
    local token=$1
    
    log_step "Authenticating with GitHub Container Registry..."
    
    # Login to GitHub Container Registry
    echo "$token" | docker login ghcr.io -u USERNAME --password-stdin
    
    if [[ $? -eq 0 ]]; then
        log_success "Successfully authenticated with GitHub Container Registry"
    else
        log_error "Failed to authenticate with GitHub Container Registry"
        log_info "Please check your token and try again"
        exit 1
    fi
}

tag_images() {
    log_step "Tagging images for GitHub Container Registry..."
    
    # Get current date for versioning
    local date_tag=$(date +%Y%m%d)
    local commit_sha=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    
    # Tag with different versions
    local tags=(
        "latest"
        "v1.0.0"
        "$date_tag"
        "commit-$commit_sha"
    )
    
    for tag in "${tags[@]}"; do
        log_info "Tagging as ${FULL_IMAGE_NAME}:${tag}"
        docker tag "${FULL_IMAGE_NAME}:latest" "${FULL_IMAGE_NAME}:${tag}"
    done
    
    log_success "Images tagged successfully"
}

push_images() {
    log_step "Pushing images to GitHub Container Registry..."
    
    # Get all tags for our image
    local tags=$(docker images "${FULL_IMAGE_NAME}" --format "{{.Tag}}" | sort -u)
    
    for tag in $tags; do
        log_info "Pushing ${FULL_IMAGE_NAME}:${tag}..."
        
        if docker push "${FULL_IMAGE_NAME}:${tag}"; then
            log_success "Successfully pushed ${FULL_IMAGE_NAME}:${tag}"
        else
            log_error "Failed to push ${FULL_IMAGE_NAME}:${tag}"
            return 1
        fi
    done
    
    log_success "All images pushed successfully"
}

set_package_visibility() {
    log_step "Setting package visibility..."
    
    log_info "Package visibility can be managed at:"
    echo "  https://github.com/orgs/truxt-ai/packages/container/adk-analyst/settings"
    echo ""
    log_info "You may want to:"
    echo "  1. Set package visibility (public/private)"
    echo "  2. Add package description"
    echo "  3. Configure access permissions"
    echo "  4. Set up package retention policies"
}

show_package_info() {
    log_step "Package Information"
    
    echo ""
    echo "📦 Package Details:"
    echo "   Registry: GitHub Container Registry (ghcr.io)"
    echo "   Organization: truxt-ai"
    echo "   Package: adk-analyst"
    echo "   Full Name: ${FULL_IMAGE_NAME}"
    echo ""
    echo "🌐 GitHub Packages URLs:"
    echo "   Package Page: https://github.com/orgs/truxt-ai/packages/container/package/adk-analyst"
    echo "   Organization Packages: https://github.com/orgs/truxt-ai/packages"
    echo ""
    echo "🐳 Docker Pull Commands:"
    echo "   docker pull ${FULL_IMAGE_NAME}:latest"
    echo "   docker pull ${FULL_IMAGE_NAME}:v1.0.0"
    echo ""
    echo "📊 Image Information:"
    docker images "${FULL_IMAGE_NAME}" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
}

cleanup() {
    log_step "Cleaning up..."
    
    # Remove authentication (optional)
    read -p "Remove GitHub Container Registry authentication? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker logout ghcr.io
        log_success "Logged out from GitHub Container Registry"
    fi
}

main() {
    local github_token=${1:-}
    
    echo "🐳 Push to GitHub Container Registry"
    echo "===================================="
    echo ""
    
    # Show usage if help requested
    if [[ "$1" == "-h" || "$1" == "--help" ]]; then
        show_usage
        exit 0
    fi
    
    log_info "Target: ${FULL_IMAGE_NAME}"
    log_info "Organization: ${GITHUB_ORG}"
    log_info "Repository: ${REPO_NAME}"
    echo ""
    
    # Run deployment steps
    check_prerequisites
    
    # Get GitHub token
    github_token=$(get_github_token "$github_token")
    if [[ -z "$github_token" ]]; then
        log_error "GitHub token is required"
        exit 1
    fi
    
    # Authenticate and push
    authenticate_github "$github_token"
    tag_images
    push_images
    
    # Show results
    echo ""
    log_success "🎉 Successfully pushed to GitHub Container Registry!"
    echo ""
    
    show_package_info
    set_package_visibility
    
    # Optional cleanup
    echo ""
    cleanup
    
    echo ""
    log_success "✅ Deployment to GitHub Packages completed!"
    echo ""
    echo "🔗 View your package at: https://github.com/orgs/truxt-ai/packages"
}

# Run main function with all arguments
main "$@"
