#!/bin/bash

# Quick Test Script for ADK Analyst Jenkins Reader Agent
# Usage: ./scripts/quick-test.sh

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 ADK Analyst Quick Test${NC}"
echo "========================="
echo ""

# Check if we're in the right directory
if [[ ! -f "adk.yaml" ]]; then
    echo -e "${RED}❌ Please run this script from the project root directory${NC}"
    exit 1
fi

# Check virtual environment
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo -e "${YELLOW}⚠️  Virtual environment not active. Activating...${NC}"
    source venv/bin/activate || {
        echo -e "${RED}❌ Failed to activate virtual environment${NC}"
        exit 1
    }
fi

# Set credentials if available
if [[ -f "service-account-key.json" ]]; then
    export GOOGLE_APPLICATION_CREDENTIALS=./service-account-key.json
    echo -e "${GREEN}✅ Google Cloud credentials configured${NC}"
else
    echo -e "${YELLOW}⚠️  service-account-key.json not found${NC}"
fi

# Test 1: Basic agent functionality
echo ""
echo -e "${BLUE}🧪 Test 1: Basic Agent Functionality${NC}"
echo "------------------------------------"
if python test_simple_cli.py; then
    echo -e "${GREEN}✅ Basic agent test passed${NC}"
else
    echo -e "${RED}❌ Basic agent test failed${NC}"
    exit 1
fi

# Test 2: Docker build
echo ""
echo -e "${BLUE}🧪 Test 2: Docker Build${NC}"
echo "----------------------"
if docker build -f Dockerfile.adk -t adk-analyst-test . > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Docker build successful${NC}"
    # Clean up
    docker rmi adk-analyst-test > /dev/null 2>&1 || true
else
    echo -e "${RED}❌ Docker build failed${NC}"
    exit 1
fi

# Test 3: Git status
echo ""
echo -e "${BLUE}🧪 Test 3: Git Repository${NC}"
echo "-------------------------"
if git status > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Git repository is properly configured${NC}"
    
    # Check if sensitive files are ignored
    if git check-ignore service-account-key.json > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Sensitive files properly ignored${NC}"
    else
        echo -e "${YELLOW}⚠️  service-account-key.json not ignored${NC}"
    fi
else
    echo -e "${RED}❌ Git repository issues${NC}"
    exit 1
fi

# Test 4: File structure
echo ""
echo -e "${BLUE}🧪 Test 4: File Structure${NC}"
echo "-------------------------"
required_files=(
    "README.md"
    "adk.yaml"
    "requirements.txt"
    "Dockerfile.adk"
    "jenkins_agent/agent.py"
    "docs/setup.md"
    ".gitignore"
)

all_files_exist=true
for file in "${required_files[@]}"; do
    if [[ -f "$file" ]]; then
        echo -e "${GREEN}✅ $file${NC}"
    else
        echo -e "${RED}❌ $file missing${NC}"
        all_files_exist=false
    fi
done

if $all_files_exist; then
    echo -e "${GREEN}✅ All required files present${NC}"
else
    echo -e "${RED}❌ Some required files missing${NC}"
    exit 1
fi

# Summary
echo ""
echo -e "${GREEN}🎉 All quick tests passed!${NC}"
echo ""
echo "Your ADK Analyst setup is working correctly."
echo ""
echo "Next steps:"
echo "1. Configure your .env file: cp .env.example .env"
echo "2. Add your service account key: service-account-key.json"
echo "3. Test with Jenkins: python examples/basic_usage.py"
echo "4. Start web interface: python -m google.adk.cli web . --port 8000"
echo "5. Deploy: ./scripts/deploy.sh development"
echo ""
echo -e "${BLUE}📚 Documentation: docs/setup.md${NC}"
echo -e "${BLUE}🐳 Docker: docker run -p 8000:8000 ghcr.io/truxt-ai/adk-analyst:latest${NC}"
