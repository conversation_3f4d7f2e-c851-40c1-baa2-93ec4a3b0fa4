#!/bin/bash

# Deployment Script for ADK Analyst Jenkins Reader Agent
# Usage: ./scripts/deploy.sh [environment] [version]

set -e

# Configuration
ENVIRONMENTS=("development" "staging" "production")
DEFAULT_VERSION="latest"
REGISTRY="ghcr.io/truxt-ai"
IMAGE_NAME="adk-analyst"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

show_usage() {
    echo "Usage: $0 [environment] [version]"
    echo ""
    echo "Environments:"
    for env in "${ENVIRONMENTS[@]}"; do
        echo "  - $env"
    done
    echo ""
    echo "Examples:"
    echo "  $0 development latest"
    echo "  $0 staging v1.2.0"
    echo "  $0 production v1.2.0"
}

validate_environment() {
    local env=$1
    for valid_env in "${ENVIRONMENTS[@]}"; do
        if [[ "$env" == "$valid_env" ]]; then
            return 0
        fi
    done
    return 1
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running"
        exit 1
    fi
    
    # Check kubectl for Kubernetes deployments
    if command -v kubectl &> /dev/null; then
        log_success "kubectl found"
    else
        log_warning "kubectl not found - Kubernetes deployments will not be available"
    fi
    
    log_success "Prerequisites check completed"
}

deploy_docker_compose() {
    local environment=$1
    local version=$2
    
    log_info "Deploying with Docker Compose for $environment..."
    
    # Set environment variables
    export ENVIRONMENT=$environment
    export IMAGE_VERSION=$version
    export FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${version}"
    
    # Choose the right compose file
    local compose_file="docker-compose.yml"
    if [[ "$environment" == "production" ]]; then
        compose_file="docker-compose.prod.yml"
    fi
    
    if [[ ! -f "$compose_file" ]]; then
        log_error "Compose file $compose_file not found"
        exit 1
    fi
    
    # Pull the latest image
    log_info "Pulling Docker image: $FULL_IMAGE_NAME"
    docker pull "$FULL_IMAGE_NAME"
    
    # Deploy
    log_info "Starting services with $compose_file..."
    docker-compose -f "$compose_file" up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 10
    
    # Health check
    local health_url="http://localhost:8000/health"
    if curl -f "$health_url" > /dev/null 2>&1; then
        log_success "Health check passed: $health_url"
    else
        log_warning "Health check failed: $health_url"
    fi
    
    log_success "Docker Compose deployment completed"
}

deploy_kubernetes() {
    local environment=$1
    local version=$2
    
    log_info "Deploying to Kubernetes for $environment..."
    
    local k8s_dir="k8s/$environment"
    
    if [[ ! -d "$k8s_dir" ]]; then
        log_error "Kubernetes manifests directory not found: $k8s_dir"
        exit 1
    fi
    
    # Update image version in manifests
    log_info "Updating image version to $version in Kubernetes manifests..."
    find "$k8s_dir" -name "*.yaml" -exec sed -i "s|image: .*adk-analyst:.*|image: ${REGISTRY}/${IMAGE_NAME}:${version}|g" {} \;
    
    # Apply manifests
    log_info "Applying Kubernetes manifests..."
    kubectl apply -f "$k8s_dir/"
    
    # Wait for rollout
    log_info "Waiting for deployment rollout..."
    kubectl rollout status deployment/adk-analyst -n "$environment" --timeout=300s
    
    # Get service information
    log_info "Service information:"
    kubectl get services -n "$environment" | grep adk-analyst
    
    log_success "Kubernetes deployment completed"
}

run_post_deployment_tests() {
    local environment=$1
    
    log_info "Running post-deployment tests for $environment..."
    
    # Basic connectivity test
    local test_url
    case $environment in
        "development")
            test_url="http://localhost:8000"
            ;;
        "staging")
            test_url="https://staging.adk-analyst.truxt.ai"
            ;;
        "production")
            test_url="https://adk-analyst.truxt.ai"
            ;;
    esac
    
    # Wait a bit for services to stabilize
    sleep 5
    
    # Test health endpoint
    if curl -f "$test_url/health" > /dev/null 2>&1; then
        log_success "Health endpoint test passed"
    else
        log_warning "Health endpoint test failed"
    fi
    
    # Test agent endpoint (if available)
    if curl -f "$test_url/api/agent/info" > /dev/null 2>&1; then
        log_success "Agent info endpoint test passed"
    else
        log_warning "Agent info endpoint test failed or not available"
    fi
    
    log_success "Post-deployment tests completed"
}

cleanup_old_deployments() {
    local environment=$1
    
    log_info "Cleaning up old deployments for $environment..."
    
    # Remove old Docker images (keep last 3 versions)
    log_info "Cleaning up old Docker images..."
    docker images "${REGISTRY}/${IMAGE_NAME}" --format "{{.Tag}}" | tail -n +4 | xargs -r docker rmi "${REGISTRY}/${IMAGE_NAME}:" 2>/dev/null || true
    
    # Clean up unused Docker resources
    docker system prune -f > /dev/null 2>&1 || true
    
    log_success "Cleanup completed"
}

main() {
    local environment=${1:-}
    local version=${2:-$DEFAULT_VERSION}
    
    echo "🚀 ADK Analyst Deployment Script"
    echo "================================"
    echo ""
    
    # Validate arguments
    if [[ -z "$environment" ]]; then
        log_error "Environment is required"
        show_usage
        exit 1
    fi
    
    if ! validate_environment "$environment"; then
        log_error "Invalid environment: $environment"
        show_usage
        exit 1
    fi
    
    log_info "Deploying to: $environment"
    log_info "Version: $version"
    log_info "Image: ${REGISTRY}/${IMAGE_NAME}:${version}"
    echo ""
    
    # Confirm deployment
    if [[ "$environment" == "production" ]]; then
        read -p "⚠️  You are about to deploy to PRODUCTION. Are you sure? (yes/no): " confirm
        if [[ "$confirm" != "yes" ]]; then
            log_info "Deployment cancelled"
            exit 0
        fi
    fi
    
    # Run deployment steps
    check_prerequisites
    
    # Choose deployment method
    if command -v kubectl &> /dev/null && [[ -d "k8s/$environment" ]]; then
        deploy_kubernetes "$environment" "$version"
    else
        deploy_docker_compose "$environment" "$version"
    fi
    
    run_post_deployment_tests "$environment"
    cleanup_old_deployments "$environment"
    
    echo ""
    log_success "🎉 Deployment to $environment completed successfully!"
    echo ""
    
    # Show access information
    case $environment in
        "development")
            echo "🌐 Access your application at: http://localhost:8000"
            ;;
        "staging")
            echo "🌐 Access your application at: https://staging.adk-analyst.truxt.ai"
            ;;
        "production")
            echo "🌐 Access your application at: https://adk-analyst.truxt.ai"
            ;;
    esac
    
    echo "📊 Monitor logs with: docker-compose logs -f"
    echo "🔍 Check status with: docker-compose ps"
}

# Run main function with all arguments
main "$@"
