#!/bin/bash

# Build and Push Docker Image Script for ADK Analyst Jenkins Reader Agent
# Usage: ./scripts/build-and-push.sh [version] [registry]

set -e

# Configuration
DEFAULT_VERSION="latest"
DEFAULT_REGISTRY="ghcr.io/truxt-ai"
IMAGE_NAME="adk-analyst"

# Parse arguments
VERSION=${1:-$DEFAULT_VERSION}
REGISTRY=${2:-$DEFAULT_REGISTRY}
FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${VERSION}"

echo "🚀 Building and pushing Docker image: ${FULL_IMAGE_NAME}"
echo "=================================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Error: Docker is not running"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "Dockerfile.adk" ]; then
    echo "❌ Error: Dockerfile.adk not found. Please run from project root."
    exit 1
fi

# Build the image
echo "🔨 Building Docker image..."
docker build -f Dockerfile.adk -t "${FULL_IMAGE_NAME}" .

# Also tag as latest if not already latest
if [ "$VERSION" != "latest" ]; then
    echo "🏷️  Tagging as latest..."
    docker tag "${FULL_IMAGE_NAME}" "${REGISTRY}/${IMAGE_NAME}:latest"
fi

# Test the image
echo "🧪 Testing the built image..."
docker run --rm "${FULL_IMAGE_NAME}" python -c "from jenkins_agent.agent import root_agent; print('✅ Image test passed')"

# Push to registry
echo "📤 Pushing to registry..."
docker push "${FULL_IMAGE_NAME}"

if [ "$VERSION" != "latest" ]; then
    docker push "${REGISTRY}/${IMAGE_NAME}:latest"
fi

echo "✅ Successfully built and pushed: ${FULL_IMAGE_NAME}"

# Show image info
echo ""
echo "📊 Image Information:"
docker images "${REGISTRY}/${IMAGE_NAME}" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

echo ""
echo "🎯 Usage:"
echo "  docker run -p 8000:8000 ${FULL_IMAGE_NAME}"
echo ""
echo "🌐 GitHub Container Registry:"
echo "  https://github.com/truxt-ai/adk-analyst/pkgs/container/adk-analyst"
