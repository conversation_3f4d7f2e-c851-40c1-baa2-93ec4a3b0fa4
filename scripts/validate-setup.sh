#!/bin/bash

# Comprehensive Setup Validation Script for ADK Analyst Jenkins Reader Agent
# Usage: ./scripts/validate-setup.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Counters
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

# Functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
    ((TESTS_PASSED++))
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
    ((TESTS_FAILED++))
}

log_test() {
    echo -e "${PURPLE}🧪 $1${NC}"
    ((TESTS_TOTAL++))
}

run_test() {
    local test_name="$1"
    local test_command="$2"
    
    log_test "Testing: $test_name"
    
    if eval "$test_command" > /dev/null 2>&1; then
        log_success "$test_name"
        return 0
    else
        log_error "$test_name"
        return 1
    fi
}

check_file_exists() {
    local file="$1"
    local description="$2"
    
    log_test "Checking: $description"
    
    if [[ -f "$file" ]]; then
        log_success "$description exists"
        return 0
    else
        log_error "$description missing: $file"
        return 1
    fi
}

check_directory_exists() {
    local dir="$1"
    local description="$2"
    
    log_test "Checking: $description"
    
    if [[ -d "$dir" ]]; then
        log_success "$description exists"
        return 0
    else
        log_error "$description missing: $dir"
        return 1
    fi
}

validate_environment() {
    echo ""
    echo "🔧 Environment Validation"
    echo "========================="
    
    # Check Python version
    log_test "Python version (3.12+)"
    if command -v python >/dev/null 2>&1; then
        python_version=$(python --version 2>&1 | cut -d' ' -f2)
        if python -c "import sys; exit(0 if sys.version_info >= (3, 12) else 1)" 2>/dev/null; then
            log_success "Python $python_version"
        else
            log_error "Python version $python_version (requires 3.12+)"
        fi
    else
        log_error "Python not found"
    fi
    
    # Check virtual environment
    log_test "Virtual environment"
    if [[ "$VIRTUAL_ENV" != "" ]]; then
        log_success "Virtual environment active: $VIRTUAL_ENV"
    else
        log_warning "Virtual environment not active"
    fi
    
    # Check Docker
    run_test "Docker installation" "command -v docker"
    run_test "Docker daemon running" "docker info"
    
    # Check Git
    run_test "Git installation" "command -v git"
    run_test "Git repository" "git status"
    
    # Check Google Cloud credentials
    log_test "Google Cloud credentials"
    if [[ -n "$GOOGLE_APPLICATION_CREDENTIALS" ]]; then
        if [[ -f "$GOOGLE_APPLICATION_CREDENTIALS" ]]; then
            log_success "Google Cloud credentials configured"
        else
            log_error "Google Cloud credentials file not found: $GOOGLE_APPLICATION_CREDENTIALS"
        fi
    else
        log_warning "GOOGLE_APPLICATION_CREDENTIALS not set"
    fi
}

validate_project_structure() {
    echo ""
    echo "📁 Project Structure Validation"
    echo "==============================="
    
    # Core files
    check_file_exists "README.md" "Main documentation"
    check_file_exists "adk.yaml" "ADK configuration"
    check_file_exists "requirements.txt" "Python dependencies"
    check_file_exists ".gitignore" "Git ignore rules"
    check_file_exists ".env.example" "Environment template"
    check_file_exists "service-account-key.json.template" "Credential template"
    
    # Docker files
    check_file_exists "Dockerfile.adk" "Production Dockerfile"
    check_file_exists "docker-compose.yml" "Development compose"
    check_file_exists "docker-compose.prod.yml" "Production compose"
    
    # Core directories
    check_directory_exists "jenkins_agent" "Main agent package"
    check_directory_exists "jenkins_agent/tools" "Agent tools"
    check_directory_exists "jenkins_agent/utils" "Agent utilities"
    check_directory_exists "docs" "Documentation"
    check_directory_exists "scripts" "Utility scripts"
    check_directory_exists "examples" "Usage examples"
    check_directory_exists ".github/workflows" "GitHub Actions"
    
    # Test files
    check_file_exists "test_simple_cli.py" "Basic CLI test"
    check_file_exists "TEST_RESULTS.md" "Test results documentation"
    
    # Scripts
    check_file_exists "scripts/build-and-push.sh" "Docker build script"
    check_file_exists "scripts/deploy.sh" "Deployment script"
    
    # Documentation
    check_file_exists "docs/setup.md" "Setup guide"
    check_file_exists "docs/development.md" "Development guide"
    check_file_exists "DEPLOYMENT_SUMMARY.md" "Deployment summary"
}

validate_python_dependencies() {
    echo ""
    echo "🐍 Python Dependencies Validation"
    echo "================================="
    
    # Check if requirements are installed
    log_test "Google ADK installation"
    if python -c "import google.adk" 2>/dev/null; then
        log_success "Google ADK installed"
    else
        log_error "Google ADK not installed"
    fi
    
    # Check agent imports
    log_test "Jenkins agent imports"
    if python -c "from jenkins_agent.agent import root_agent" 2>/dev/null; then
        log_success "Jenkins agent imports working"
    else
        log_error "Jenkins agent import failed"
    fi
    
    # Check tool imports
    log_test "Jenkins tools imports"
    if python -c "from jenkins_agent.tools import validate_jenkins_connection" 2>/dev/null; then
        log_success "Jenkins tools imports working"
    else
        log_error "Jenkins tools import failed"
    fi
}

validate_agent_functionality() {
    echo ""
    echo "🤖 Agent Functionality Validation"
    echo "================================="
    
    # Test basic agent loading
    log_test "Agent initialization"
    if python -c "from jenkins_agent.agent import root_agent; print(f'Agent: {root_agent.name}')" > /dev/null 2>&1; then
        log_success "Agent initialization"
    else
        log_error "Agent initialization failed"
    fi
    
    # Test agent info
    log_test "Agent information retrieval"
    if python -c "from jenkins_agent.agent import get_agent_info; info = get_agent_info(); print(f'Tools: {len(info[\"tools\"])}')" > /dev/null 2>&1; then
        log_success "Agent information retrieval"
    else
        log_error "Agent information retrieval failed"
    fi
    
    # Test model configuration
    log_test "Model configuration"
    if python -c "from jenkins_agent.agent import get_available_models; models = get_available_models(); print(f'Models: {len(models[\"models\"])}')" > /dev/null 2>&1; then
        log_success "Model configuration"
    else
        log_error "Model configuration failed"
    fi
}

validate_docker_setup() {
    echo ""
    echo "🐳 Docker Setup Validation"
    echo "=========================="
    
    # Check if Docker image exists locally
    log_test "Local Docker image"
    if docker images ghcr.io/truxt-ai/adk-analyst:latest --format "{{.Repository}}" | grep -q "ghcr.io/truxt-ai/adk-analyst"; then
        log_success "Local Docker image exists"
    else
        log_warning "Local Docker image not found (will be built)"
    fi
    
    # Test Docker build
    log_test "Docker build capability"
    if docker build -f Dockerfile.adk -t adk-analyst-test . > /dev/null 2>&1; then
        log_success "Docker build successful"
        # Clean up test image
        docker rmi adk-analyst-test > /dev/null 2>&1 || true
    else
        log_error "Docker build failed"
    fi
}

validate_git_setup() {
    echo ""
    echo "📚 Git Repository Validation"
    echo "============================"
    
    # Check Git status
    log_test "Git repository status"
    if git status > /dev/null 2>&1; then
        log_success "Git repository initialized"
    else
        log_error "Git repository not initialized"
    fi
    
    # Check remote origin
    log_test "Git remote origin"
    if git remote get-url origin | grep -q "truxt-ai/adk-analyst"; then
        log_success "Git remote origin configured"
    else
        log_error "Git remote origin not configured"
    fi
    
    # Check if sensitive files are ignored
    log_test "Sensitive files ignored"
    if git check-ignore service-account-key.json > /dev/null 2>&1; then
        log_success "service-account-key.json properly ignored"
    else
        log_error "service-account-key.json not ignored"
    fi
    
    if git check-ignore .env > /dev/null 2>&1; then
        log_success ".env properly ignored"
    else
        log_warning ".env not ignored (should be in .gitignore)"
    fi
}

validate_security() {
    echo ""
    echo "🔐 Security Validation"
    echo "======================"
    
    # Check if sensitive files exist but are not tracked
    log_test "Credential file security"
    if [[ -f "service-account-key.json" ]]; then
        if git ls-files --error-unmatch service-account-key.json > /dev/null 2>&1; then
            log_error "service-account-key.json is tracked by Git (SECURITY RISK!)"
        else
            log_success "service-account-key.json exists but not tracked"
        fi
    else
        log_warning "service-account-key.json not found (use template to create)"
    fi
    
    # Check .env file
    log_test "Environment file security"
    if [[ -f ".env" ]]; then
        if git ls-files --error-unmatch .env > /dev/null 2>&1; then
            log_error ".env is tracked by Git (SECURITY RISK!)"
        else
            log_success ".env exists but not tracked"
        fi
    else
        log_warning ".env not found (copy from .env.example)"
    fi
    
    # Check file permissions
    log_test "File permissions"
    if [[ -f "service-account-key.json" ]]; then
        perms=$(stat -c "%a" service-account-key.json 2>/dev/null || stat -f "%A" service-account-key.json 2>/dev/null)
        if [[ "$perms" == "600" ]]; then
            log_success "service-account-key.json has secure permissions (600)"
        else
            log_warning "service-account-key.json permissions: $perms (should be 600)"
        fi
    fi
}

run_integration_tests() {
    echo ""
    echo "🧪 Integration Tests"
    echo "==================="
    
    # Run basic CLI test
    log_test "Basic CLI functionality"
    if python test_simple_cli.py > /dev/null 2>&1; then
        log_success "Basic CLI test passed"
    else
        log_error "Basic CLI test failed"
    fi
    
    # Test ADK web command availability
    log_test "ADK web command"
    if python -m google.adk.cli web --help > /dev/null 2>&1; then
        log_success "ADK web command available"
    else
        log_error "ADK web command not available"
    fi
}

generate_report() {
    echo ""
    echo "📊 Validation Report"
    echo "==================="
    echo ""
    echo "Total Tests: $TESTS_TOTAL"
    echo "Passed: $TESTS_PASSED"
    echo "Failed: $TESTS_FAILED"
    echo ""
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}🎉 All validations passed! Your setup is ready for deployment.${NC}"
        echo ""
        echo "Next steps:"
        echo "1. Configure your .env file with actual values"
        echo "2. Add your Google Cloud service account key"
        echo "3. Test with your Jenkins instance"
        echo "4. Deploy using: ./scripts/deploy.sh development"
        return 0
    else
        echo -e "${RED}❌ Some validations failed. Please fix the issues above.${NC}"
        echo ""
        echo "Common fixes:"
        echo "1. Install missing dependencies: pip install -r requirements.txt"
        echo "2. Set up Google Cloud credentials"
        echo "3. Configure environment variables"
        echo "4. Check file permissions"
        return 1
    fi
}

main() {
    echo "🔍 ADK Analyst Setup Validation"
    echo "==============================="
    echo ""
    echo "This script validates your ADK Analyst Jenkins Reader Agent setup."
    echo "It checks environment, dependencies, configuration, and security."
    echo ""
    
    # Check if we're in the right directory
    if [[ ! -f "adk.yaml" ]]; then
        log_error "Please run this script from the project root directory"
        exit 1
    fi
    
    # Run all validations
    validate_environment
    validate_project_structure
    validate_python_dependencies
    validate_agent_functionality
    validate_docker_setup
    validate_git_setup
    validate_security
    run_integration_tests
    
    # Generate final report
    generate_report
}

# Run main function
main "$@"
