# Getting Started with ADK Analyst Jenkins Reader Agent

Welcome to the ADK Analyst Jenkins Reader Agent! This guide will help you get up and running quickly.

## 🚀 Quick Start (5 minutes)

### 1. <PERSON><PERSON> and Setup
```bash
# Clone the repository
<NAME_EMAIL>:truxt-ai/adk-analyst.git
cd adk-analyst

# Run quick validation
./scripts/quick-test.sh
```

### 2. Configure Environment
```bash
# Copy environment template
cp .env.example .env

# Edit with your settings (use your favorite editor)
nano .env
```

### 3. Add Google Cloud Credentials
```bash
# Copy your service account key to the project root
cp /path/to/your/service-account-key.json ./service-account-key.json

# Set environment variable
export GOOGLE_APPLICATION_CREDENTIALS=./service-account-key.json
```

### 4. Test the Setup
```bash
# Test basic functionality
python test_simple_cli.py

# Test with examples
python examples/basic_usage.py
```

### 5. Start Web Interface
```bash
# Start the web interface
python -m google.adk.cli web . --port 8000

# Open in browser: http://localhost:8000
```

## 🐳 Docker Quick Start (2 minutes)

### Option 1: Use Pre-built Image
```bash
# Pull and run the latest image
docker run -p 8000:8000 \
  -v /path/to/your/service-account-key.json:/app/service-account-key.json \
  ghcr.io/truxt-ai/adk-analyst:latest

# Access at: http://localhost:8000
```

### Option 2: Build Locally
```bash
# Build the image
docker build -f Dockerfile.adk -t adk-analyst .

# Run the container
docker run -p 8000:8000 adk-analyst
```

## 📋 Prerequisites

### Required
- **Python 3.12+**
- **Docker** (for containerized deployment)
- **Git** (for version control)
- **Google Cloud Project** with Vertex AI enabled
- **Jenkins Server** with API access

### Optional
- **kubectl** (for Kubernetes deployment)
- **Poetry** (alternative to pip)

## 🔧 Configuration

### Environment Variables (.env)
```bash
# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_GENAI_USE_VERTEXAI=TRUE

# Jenkins Configuration
JENKINS_CREDENTIALS_SECRET=jenkins-credentials
ALLOWED_JENKINS_DOMAINS=jenkins.example.com,localhost
JENKINS_PASSWORD=your-jenkins-password

# Application Configuration
LOG_LEVEL=INFO
MAX_CONCURRENT_REQUESTS=10
RATE_LIMIT_PER_MINUTE=100
```

### Google Cloud Service Account
1. Create a service account in Google Cloud Console
2. Grant these roles:
   - `Vertex AI User`
   - `AI Platform Developer`
   - `Logs Writer`
3. Download the JSON key file
4. Place it as `service-account-key.json` in the project root

## 🧪 Testing Your Setup

### Quick Test
```bash
./scripts/quick-test.sh
```

### Comprehensive Test
```bash
./scripts/validate-setup.sh
```

### Manual Tests
```bash
# Test agent loading
python -c "from jenkins_agent.agent import root_agent; print(f'Agent: {root_agent.name}')"

# Test tools
python -c "from jenkins_agent.agent import get_agent_info; print(get_agent_info())"

# Test Docker build
docker build -f Dockerfile.adk -t test-image .
```

## 🛠️ Available Tools

The agent includes 6 specialized Jenkins tools:

1. **validate_jenkins_connection** - Test connectivity and server info
2. **get_jenkins_server_status** - Get server health and status
3. **get_jenkins_jobs** - List and filter Jenkins jobs
4. **get_job_config** - Retrieve job configurations
5. **get_build_history** - Analyze build history and trends
6. **get_artifacts** - Access build artifacts and metadata

## 💻 Usage Examples

### CLI Usage
```bash
# Basic agent test
python test_simple_cli.py

# Interactive examples
python examples/basic_usage.py

# Direct agent usage
python -c "
from jenkins_agent.agent import root_agent
import asyncio

async def test():
    async for response in root_agent.run_async('What tools do you have?'):
        print(response)

asyncio.run(test())
"
```

### Web Interface
```bash
# Start web server
python -m google.adk.cli web . --port 8000

# Access at http://localhost:8000
# Use the interactive chat interface
```

### Docker Usage
```bash
# Development
docker-compose up -d

# Production
docker-compose -f docker-compose.prod.yml up -d

# Custom run
docker run -d \
  -p 8000:8000 \
  -e GOOGLE_CLOUD_PROJECT=your-project \
  -v /path/to/service-account-key.json:/app/service-account-key.json \
  ghcr.io/truxt-ai/adk-analyst:latest
```

## 🚀 Deployment

### Development
```bash
./scripts/deploy.sh development latest
```

### Staging
```bash
./scripts/deploy.sh staging v1.0.0
```

### Production
```bash
./scripts/deploy.sh production v1.0.0
```

## 🔐 Security Best Practices

### Credential Management
- ✅ Never commit `service-account-key.json` to Git
- ✅ Use environment variables for sensitive data
- ✅ Rotate credentials regularly
- ✅ Use least-privilege access principles

### File Permissions
```bash
# Secure the service account key
chmod 600 service-account-key.json

# Verify it's ignored by Git
git check-ignore service-account-key.json
```

### Network Security
- Use HTTPS for Jenkins connections
- Implement proper firewall rules
- Consider VPN or private network access
- Enable audit logging for compliance

## 🐛 Troubleshooting

### Common Issues

#### 1. Import Errors
```bash
# Solution: Activate virtual environment and install dependencies
source venv/bin/activate
pip install -r requirements.txt
```

#### 2. Authentication Errors
```bash
# Solution: Check Google Cloud credentials
export GOOGLE_APPLICATION_CREDENTIALS=./service-account-key.json
python -c "import google.auth; print('Credentials OK')"
```

#### 3. Docker Issues
```bash
# Solution: Check Docker daemon
docker info

# Rebuild image
docker build -f Dockerfile.adk -t adk-analyst .
```

#### 4. Port Already in Use
```bash
# Solution: Use different port or kill existing process
python -m google.adk.cli web . --port 8001
# or
lsof -ti:8000 | xargs kill -9
```

### Getting Help
- 📖 Check [Setup Guide](docs/setup.md)
- 🔧 Review [Development Guide](docs/development.md)
- 📊 See [Test Results](TEST_RESULTS.md)
- 🚀 Read [Deployment Summary](DEPLOYMENT_SUMMARY.md)

## 📚 Documentation

### Core Documentation
- [README.md](README.md) - Main project overview
- [docs/setup.md](docs/setup.md) - Detailed setup instructions
- [docs/development.md](docs/development.md) - Development guide
- [docs/design.md](docs/design.md) - Architecture overview

### Operational Guides
- [TEST_RESULTS.md](TEST_RESULTS.md) - Comprehensive test validation
- [DEPLOYMENT_SUMMARY.md](DEPLOYMENT_SUMMARY.md) - Deployment status and guide
- [GETTING_STARTED.md](GETTING_STARTED.md) - This quick start guide

### Examples and Scripts
- [examples/basic_usage.py](examples/basic_usage.py) - Usage examples
- [scripts/quick-test.sh](scripts/quick-test.sh) - Quick validation
- [scripts/build-and-push.sh](scripts/build-and-push.sh) - Docker operations
- [scripts/deploy.sh](scripts/deploy.sh) - Deployment automation

## 🎯 Next Steps

### For Development
1. Set up your development environment
2. Read the [Development Guide](docs/development.md)
3. Run the test suite
4. Make your first contribution

### For Production
1. Configure production credentials
2. Set up monitoring and alerting
3. Deploy to staging first
4. Use the deployment scripts

### For Integration
1. Connect to your Jenkins instance
2. Test with real data
3. Configure security policies
4. Set up automated deployments

## 🤝 Contributing

This is a private repository. For internal contributors:

1. Create a feature branch
2. Make your changes
3. Run tests: `./scripts/quick-test.sh`
4. Submit a pull request

## 📄 License

Private repository - All rights reserved by Truxt AI.

---

**Ready to analyze your Jenkins infrastructure with AI? Let's get started!** 🚀

For immediate help: `./scripts/quick-test.sh`
