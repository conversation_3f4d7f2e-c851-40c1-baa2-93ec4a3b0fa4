# ADK Configuration for Jenkins Reader Agent
name: jenkins_agent
display_name: "Jenkins Reader Agent"
description: "Enterprise-grade Jenkins read-only agent with comprehensive data extraction capabilities"
version: "1.0.0"

# Agent configuration
agent:
  module: "jenkins_agent.agent"
  root_agent: "root_agent"
  models:
    - name: "gemini-2.5-pro"
      display_name: "Gemini 2.5 Pro"
      description: "Advanced model for complex Jenkins analysis"
      default: true
    - name: "gemini-2.5-flash"
      display_name: "Gemini 2.5 Flash"
      description: "Fast model for quick Jenkins queries"

# Tools configuration
tools:
  - name: "validate_jenkins_connection"
    description: "Test connectivity and retrieve server info"
    category: "connection"
  - name: "get_jenkins_server_status"
    description: "Get comprehensive server status and health"
    category: "connection"
  - name: "get_jenkins_jobs"
    description: "List and filter Jenkins jobs"
    category: "jobs"
  - name: "get_job_config"
    description: "Retrieve job configurations"
    category: "jobs"
  - name: "get_build_history"
    description: "Analyze build history and trends"
    category: "builds"
  - name: "get_artifacts"
    description: "Access build artifacts and metadata"
    category: "builds"

# Security configuration
security:
  read_only: true
  authentication_required: true
  audit_logging: true
  rate_limiting: true

# Environment configuration
environment:
  google_cloud_project: "${GOOGLE_CLOUD_PROJECT:-test-project}"
  jenkins_credentials_secret: "${JENKINS_CREDENTIALS_SECRET:-jenkins-credentials}"
  allowed_jenkins_domains: "${ALLOWED_JENKINS_DOMAINS:-jenkins.truxt.ai,localhost}"
  debug: "${DEBUG:-false}"

# Web interface configuration
web:
  title: "Jenkins Reader Agent"
  description: "Enterprise-grade Jenkins read-only agent"
  favicon: "/adk_favicon.svg"
  theme: "default"

# Capabilities
capabilities:
  - "Jenkins server connectivity validation"
  - "Job listing and filtering"
  - "Job configuration analysis"
  - "Build history analysis"
  - "Artifact management"
  - "Performance monitoring"
  - "Security auditing"
  - "Dependency mapping"

# Enterprise features
enterprise_features:
  - "Google Cloud IAM integration"
  - "Structured logging and monitoring"
  - "Auto-scaling and high availability"
  - "Compliance and audit trails"
  - "Performance optimization"
  - "Error handling and recovery"
