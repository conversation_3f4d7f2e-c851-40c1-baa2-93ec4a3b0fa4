version: '3.8'

services:
  jenkins-reader-agent:
    build:
      context: .
      dockerfile: Dockerfile.adk
    container_name: jenkins-reader-agent
    ports:
      - "8000:8000"
    environment:
      - GOOGLE_GENAI_USE_VERTEXAI=1
      - GOOGLE_CLOUD_PROJECT=truxtsaas
      - GOOGLE_CLOUD_LOCATION=us-central1
      - GOOGLE_APPLICATION_CREDENTIALS=/app/service-account-key.json
      - JENKINS_CREDENTIALS_SECRET=jenkins-credentials
      - ALLOWED_JENKINS_DOMAINS=jenkins.truxt.ai,localhost,jenkins.example.com
      - LOG_LEVEL=INFO
      - ENABLE_AUDIT_LOGGING=true
      - ENABLE_RATE_LIMITING=true
      - DEBUG=false
    volumes:
      # Mount service account key as a secret (recommended for production)
      - ./service-account-key.json:/app/service-account-key.json:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - jenkins-agent-network

networks:
  jenkins-agent-network:
    driver: bridge
