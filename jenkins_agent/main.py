"""
Main entry point for Jenkins Reader Agent.

This module provides the main entry point for running the Jenkins Reader Agent
in various modes (development, testing, production).
"""

import asyncio
import sys
from typing import Any, Dict, Optional

from .agent import jenkins_agent, get_agent_info
from .config.settings import get_settings, is_development
from .utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)


async def run_agent_interactive():
    """Run the agent in interactive mode for development/testing."""
    print("🤖 Jenkins Reader Agent - Interactive Mode")
    print("=" * 50)
    
    # Display agent information
    agent_info = get_agent_info()
    print(f"Agent: {agent_info['name']} v{agent_info['version']}")
    print(f"Model: {agent_info['model']}")
    print(f"Description: {agent_info['description']}")
    print()
    
    # Display available tools
    print("Available Tools:")
    for tool in agent_info['tools']:
        print(f"  • {tool['name']}: {tool['description']}")
    print()
    
    print("Interactive mode is not yet implemented.")
    print("Use the test script (test_agent.py) to test functionality.")
    print()
    print("Example usage:")
    print("  python test_agent.py")


async def run_agent_server():
    """Run the agent as a server (for production deployment)."""
    print("🚀 Jenkins Reader Agent - Server Mode")
    print("=" * 50)
    
    settings = get_settings()
    
    logger.info(
        "Starting Jenkins Reader Agent server",
        extra={
            "mode": "server",
            "debug": settings.debug,
            "log_level": settings.log_level
        }
    )
    
    print("Server mode is not yet implemented.")
    print("This would typically start a web server or gRPC server")
    print("to handle agent requests in a production environment.")


async def run_health_check():
    """Run a health check to verify agent functionality."""
    print("🏥 Jenkins Reader Agent - Health Check")
    print("=" * 50)
    
    try:
        # Import test functions
        from test_agent import test_settings, test_jenkins_connection
        
        # Run basic health checks
        print("Checking configuration...")
        config_ok = await test_settings()
        
        if config_ok:
            print("Checking Jenkins connectivity...")
            connection_ok = await test_jenkins_connection()
            
            if connection_ok:
                print("✅ Health check passed - Agent is ready")
                return True
            else:
                print("❌ Health check failed - Jenkins connection issues")
                return False
        else:
            print("❌ Health check failed - Configuration issues")
            return False
            
    except Exception as e:
        print(f"❌ Health check failed - {e}")
        logger.error("Health check failed", extra={"error": str(e)})
        return False


def print_usage():
    """Print usage information."""
    print("Jenkins Reader Agent")
    print("=" * 30)
    print()
    print("Usage:")
    print("  python -m jenkins_agent.main [command]")
    print()
    print("Commands:")
    print("  interactive  - Run in interactive mode (default)")
    print("  server      - Run as a server")
    print("  health      - Run health check")
    print("  info        - Show agent information")
    print("  help        - Show this help message")
    print()
    print("Examples:")
    print("  python -m jenkins_agent.main")
    print("  python -m jenkins_agent.main health")
    print("  python -m jenkins_agent.main server")


async def main():
    """Main entry point."""
    # Get command line arguments
    command = sys.argv[1] if len(sys.argv) > 1 else "interactive"
    
    try:
        if command in ["help", "-h", "--help"]:
            print_usage()
            
        elif command == "info":
            agent_info = get_agent_info()
            print("Jenkins Reader Agent Information")
            print("=" * 40)
            print(f"Name: {agent_info['name']}")
            print(f"Version: {agent_info['version']}")
            print(f"Model: {agent_info['model']}")
            print(f"Description: {agent_info['description']}")
            print()
            print("Capabilities:")
            for capability in agent_info['capabilities']:
                print(f"  • {capability}")
            print()
            print("Security Features:")
            for feature in agent_info['security_features']:
                print(f"  • {feature}")
            
        elif command == "health":
            success = await run_health_check()
            sys.exit(0 if success else 1)
            
        elif command == "server":
            await run_agent_server()
            
        elif command == "interactive":
            await run_agent_interactive()
            
        else:
            print(f"Unknown command: {command}")
            print()
            print_usage()
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  Interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error("Main execution failed", extra={"error": str(e)})
        print(f"💥 Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
