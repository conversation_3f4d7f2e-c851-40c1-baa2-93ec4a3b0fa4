"""Main Jenkins Read-Only Agent implementation using Google ADK."""

from typing import Any, Dict, Optional

from google.adk.agents import Agent
from google.adk.tools import ToolContext
from google.genai.types import GenerateContentConfig

# from .config.schemas import Jenkins<PERSON><PERSON>ponse  # Not needed for ADK agent
from .config.settings import get_settings
from .prompts import (
    jenkins_system_instructions,
    jenkins_global_context,
    format_response_guidelines
)
from .tools.connection_tools import validate_jenkins_connection, get_jenkins_server_status
from .tools.job_tools import get_jenkins_jobs, get_job_config
from .tools.build_tools import get_build_history, get_artifacts
from .utils.logging import (
    get_logger,
    log_audit_event,
    log_security_event,
    setup_logging
)
from .utils.validation import validate_request_permissions

# Initialize logging
setup_logging()
logger = get_logger(__name__)


def security_validation_callback(callback_context) -> Optional[Any]:
    """
    Pre-execution security validation callback.

    This callback runs before each agent execution to validate security
    requirements and log access attempts.

    Args:
        callback_context: ADK callback context

    Returns:
        None to continue execution, or response to override
    """
    try:
        logger.info("Jenkins agent security validation started")

        # For now, just log and continue
        # In production, this would validate user permissions, rate limits, etc.
        logger.info("Security validation passed")

        # Return None to continue with normal execution
        return None

    except Exception as e:
        logger.error(f"Security validation failed: {e}")

        # Don't fail the request due to callback issues
        return None


def audit_logging_callback(callback_context) -> Optional[Any]:
    """
    Post-execution audit logging callback.

    This callback runs after each agent execution to log the results
    and update usage metrics.

    Args:
        callback_context: ADK callback context

    Returns:
        None to continue with normal response
    """
    try:
        logger.info("Jenkins agent execution completed")

        # For now, just log completion
        # In production, this would log detailed metrics, usage stats, etc.

        # Return None to continue with normal response
        return None

    except Exception as e:
        logger.error(f"Audit logging failed: {e}")

        # Don't fail the request due to logging issues
        return None


# Create the main Jenkins Read-Only Agent with Gemini 2.5 Pro
settings = get_settings()
jenkins_agent = Agent(
    model=settings.gemini_pro_model,
    name="jenkins_read_only_agent",
    instruction=jenkins_system_instructions(),
    global_instruction=jenkins_global_context(),
    tools=[
        # Connection and validation tools
        validate_jenkins_connection,
        get_jenkins_server_status,

        # Job management tools
        get_jenkins_jobs,
        get_job_config,

        # Build and artifact tools
        get_build_history,
        get_artifacts,
    ],
    before_agent_callback=security_validation_callback,
    after_agent_callback=audit_logging_callback
)

# Create a faster agent with Gemini 2.5 Flash for quick queries
jenkins_agent_flash = Agent(
    model=settings.gemini_flash_model,
    name="jenkins_read_only_agent_flash",
    instruction=jenkins_system_instructions(),
    global_instruction=jenkins_global_context(),
    tools=[
        # Connection and validation tools
        validate_jenkins_connection,
        get_jenkins_server_status,

        # Job management tools
        get_jenkins_jobs,
        get_job_config,

        # Build and artifact tools
        get_build_history,
        get_artifacts,
    ],
    before_agent_callback=security_validation_callback,
    after_agent_callback=audit_logging_callback
)

# Set the root agent for ADK Web compatibility
root_agent = jenkins_agent


async def create_jenkins_agent_session(
    user_context: Optional[Dict[str, Any]] = None,
    model_preference: str = "pro"
) -> Agent:
    """
    Create a Jenkins agent session with user-specific context.

    Args:
        user_context: Optional user-specific context and preferences
        model_preference: Model preference ("pro" for Gemini 2.5 Pro, "flash" for Gemini 2.5 Flash)

    Returns:
        Agent: Configured Jenkins agent instance
    """
    logger.info(
        "Creating Jenkins agent session",
        extra={
            "user_context": user_context,
            "model_preference": model_preference
        }
    )

    # Select agent based on model preference
    if model_preference.lower() in ["flash", "fast", "quick"]:
        logger.info("Using Gemini 2.5 Flash for fast responses")
        return jenkins_agent_flash
    else:
        logger.info("Using Gemini 2.5 Pro for comprehensive analysis")
        return jenkins_agent


def get_available_models() -> Dict[str, Any]:
    """
    Get information about available models.

    Returns:
        Dict containing available models and their capabilities
    """
    return {
        "models": [
            {
                "id": "gemini-2.5-pro-preview-05-06",
                "name": "Gemini 2.5 Pro",
                "description": "Advanced model for complex Jenkins analysis and detailed reporting",
                "capabilities": [
                    "Complex multi-step analysis",
                    "Detailed configuration parsing",
                    "Advanced pattern recognition",
                    "Comprehensive reporting"
                ],
                "use_cases": [
                    "Detailed build analysis",
                    "Configuration troubleshooting",
                    "Performance optimization",
                    "Security auditing"
                ],
                "response_time": "slower",
                "accuracy": "highest"
            },
            {
                "id": "gemini-2.5-flash-preview-05-20",
                "name": "Gemini 2.5 Flash",
                "description": "Fast model for quick Jenkins queries and simple operations",
                "capabilities": [
                    "Quick status checks",
                    "Simple job listing",
                    "Basic build information",
                    "Fast connectivity tests"
                ],
                "use_cases": [
                    "Health checks",
                    "Job status monitoring",
                    "Quick queries",
                    "Real-time dashboards"
                ],
                "response_time": "faster",
                "accuracy": "high"
            }
        ],
        "default": "gemini-2.5-pro-preview-05-06",
        "recommendation": {
            "complex_analysis": "gemini-2.5-pro-preview-05-06",
            "quick_queries": "gemini-2.5-flash-preview-05-20",
            "real_time_monitoring": "gemini-2.5-flash-preview-05-20",
            "detailed_reporting": "gemini-2.5-pro-preview-05-06"
        }
    }


def get_agent_info() -> Dict[str, Any]:
    """
    Get information about the Jenkins agent capabilities.
    
    Returns:
        Dict containing agent information and capabilities
    """
    return {
        "name": "Jenkins Read-Only Agent",
        "version": "1.0.0",
        "description": "Enterprise-grade Jenkins analysis and reporting agent",
        "models": {
            "primary": "gemini-2.5-pro-preview-05-06",
            "fast": "gemini-2.5-flash-preview-05-20"
        },
        "capabilities": [
            "Jenkins server connectivity validation",
            "Job listing and filtering",
            "Job configuration analysis",
            "Build history analysis",
            "Artifact management",
            "Performance monitoring",
            "Security auditing",
            "Dependency mapping"
        ],
        "tools": [
            {
                "name": "validate_jenkins_connection",
                "description": "Test connectivity and retrieve server info"
            },
            {
                "name": "get_jenkins_server_status",
                "description": "Get comprehensive server status and health"
            },
            {
                "name": "get_jenkins_jobs",
                "description": "List and filter Jenkins jobs"
            },
            {
                "name": "get_job_config",
                "description": "Retrieve job configurations"
            },
            {
                "name": "get_build_history",
                "description": "Analyze build history and trends"
            },
            {
                "name": "get_artifacts",
                "description": "Access build artifacts and metadata"
            }
        ],
        "security_features": [
            "Read-only access enforcement",
            "User authentication and authorization",
            "Comprehensive audit logging",
            "Input validation and sanitization",
            "Rate limiting and abuse prevention",
            "Secure credential management"
        ],
        "enterprise_features": [
            "Google Cloud IAM integration",
            "Structured logging and monitoring",
            "Auto-scaling and high availability",
            "Compliance and audit trails",
            "Performance optimization",
            "Error handling and recovery"
        ]
    }


# Export the main agent and utility functions
__all__ = [
    "jenkins_agent",
    "jenkins_agent_flash",
    "root_agent",
    "create_jenkins_agent_session",
    "get_available_models",
    "get_agent_info",
    "security_validation_callback",
    "audit_logging_callback"
]
