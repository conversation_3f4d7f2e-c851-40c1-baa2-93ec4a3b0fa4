"""Custom exceptions for Jenkins Reader Agent."""

from typing import Any, Dict, Optional


class JenkinsAgentError(Exception):
    """Base exception for Jenkins Agent operations."""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary format."""
        return {
            "code": self.error_code,
            "message": self.message,
            "details": self.details
        }


class ConnectionError(JenkinsAgentError):
    """Jenkins connection failed."""
    
    def __init__(
        self,
        message: str = "Failed to connect to Jenkins server",
        jenkins_url: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if jenkins_url:
            details["jenkins_url"] = jenkins_url
        super().__init__(message, details=details, **kwargs)


class AuthenticationError(JenkinsAgentError):
    """Authentication with <PERSON> failed."""
    
    def __init__(
        self,
        message: str = "Authentication failed",
        username: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if username:
            details["username"] = username
        super().__init__(message, details=details, **kwargs)


class AuthorizationError(JenkinsAgentError):
    """Authorization failed - user lacks required permissions."""
    
    def __init__(
        self, 
        message: str = "User lacks required permissions",
        required_permission: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if required_permission:
            details["required_permission"] = required_permission
        super().__init__(message, details=details, **kwargs)


class UnauthorizedError(JenkinsAgentError):
    """User not authorized to perform this operation."""
    
    def __init__(
        self, 
        message: str = "User not authorized",
        **kwargs: Any
    ) -> None:
        super().__init__(message, **kwargs)


class RateLimitError(JenkinsAgentError):
    """Rate limit exceeded."""
    
    def __init__(
        self, 
        message: str = "Rate limit exceeded",
        limit: Optional[int] = None,
        reset_time: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if limit:
            details["rate_limit"] = limit
        if reset_time:
            details["reset_time"] = reset_time
        super().__init__(message, details=details, **kwargs)


class ValidationError(JenkinsAgentError):
    """Input validation failed."""
    
    def __init__(
        self, 
        message: str = "Input validation failed",
        field: Optional[str] = None,
        value: Optional[Any] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if field:
            details["field"] = field
        if value is not None:
            details["value"] = str(value)
        super().__init__(message, details=details, **kwargs)


class SecurityError(JenkinsAgentError):
    """Security violation detected."""
    
    def __init__(
        self, 
        message: str = "Security violation detected",
        violation_type: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if violation_type:
            details["violation_type"] = violation_type
        super().__init__(message, details=details, **kwargs)


class ConfigurationError(JenkinsAgentError):
    """Configuration error."""
    
    def __init__(
        self, 
        message: str = "Configuration error",
        config_key: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if config_key:
            details["config_key"] = config_key
        super().__init__(message, details=details, **kwargs)


class JenkinsAPIError(JenkinsAgentError):
    """Jenkins API returned an error."""
    
    def __init__(
        self, 
        message: str = "Jenkins API error",
        status_code: Optional[int] = None,
        response_body: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if status_code:
            details["status_code"] = status_code
        if response_body:
            details["response_body"] = response_body
        super().__init__(message, details=details, **kwargs)


class TimeoutError(JenkinsAgentError):
    """Operation timed out."""
    
    def __init__(
        self, 
        message: str = "Operation timed out",
        timeout_seconds: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if timeout_seconds:
            details["timeout_seconds"] = timeout_seconds
        super().__init__(message, details=details, **kwargs)


class DataProcessingError(JenkinsAgentError):
    """Error processing Jenkins data."""
    
    def __init__(
        self, 
        message: str = "Error processing Jenkins data",
        data_type: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        details = kwargs.pop("details", {})
        if data_type:
            details["data_type"] = data_type
        super().__init__(message, details=details, **kwargs)
