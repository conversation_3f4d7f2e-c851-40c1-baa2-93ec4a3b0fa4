apiVersion: v1
kind: Namespace
metadata:
  name: adk-analyst
  labels:
    name: adk-analyst
    environment: production

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: adk-analyst-ksa
  namespace: adk-analyst
  annotations:
    iam.gke.io/gcp-service-account: <EMAIL>

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: adk-analyst-config
  namespace: adk-analyst
data:
  GOOGLE_CLOUD_PROJECT: "truxtsaas"
  GOOGLE_CLOUD_LOCATION: "us-central1"
  GOOGLE_GENAI_USE_VERTEXAI: "TRUE"
  LOG_LEVEL: "INFO"
  MAX_CONCURRENT_REQUESTS: "10"
  RATE_LIMIT_PER_MINUTE: "100"
  ENABLE_AUDIT_LOGGING: "true"
  ENABLE_RATE_LIMITING: "true"
  MAX_RESULTS_PER_QUERY: "1000"
  CONNECTION_TIMEOUT: "30"
  READ_TIMEOUT: "60"
  MAX_RETRIES: "3"
  GEMINI_PRO_MODEL: "gemini-2.5-pro-preview-05-06"
  GEMINI_FLASH_MODEL: "gemini-2.5-flash-preview-05-20"
  DEBUG: "false"
  TESTING: "false"

---
apiVersion: v1
kind: Secret
metadata:
  name: adk-analyst-secrets
  namespace: adk-analyst
type: Opaque
data:
  # These will be populated by the deployment script
  jenkins-password: ""  # Base64 encoded
  
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: adk-analyst
  namespace: adk-analyst
  labels:
    app: adk-analyst
    component: jenkins-agent
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
  selector:
    matchLabels:
      app: adk-analyst
  template:
    metadata:
      labels:
        app: adk-analyst
        component: jenkins-agent
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: adk-analyst-ksa
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: adk-analyst
        image: ghcr.io/truxt-ai/adk-analyst:latest
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 8000
          protocol: TCP
        
        envFrom:
        - configMapRef:
            name: adk-analyst-config
        
        env:
        - name: JENKINS_CREDENTIALS_SECRET
          value: "jenkins-credentials"
        - name: ALLOWED_JENKINS_DOMAINS
          value: "jenkins.truxt.ai,localhost"
        - name: JENKINS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: adk-analyst-secrets
              key: jenkins-password
              optional: true
        
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
            ephemeral-storage: "1Gi"
          limits:
            memory: "4Gi"
            cpu: "2000m"
            ephemeral-storage: "2Gi"
        
        # Health checks
        livenessProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        
        readinessProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        
        startupProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 12
          successThreshold: 1
        
        # Security context
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
        
        # Volume mounts for temporary files
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: var-tmp
          mountPath: /var/tmp
      
      volumes:
      - name: tmp
        emptyDir: {}
      - name: var-tmp
        emptyDir: {}
      
      # Pod disruption budget
      terminationGracePeriodSeconds: 30
      
      # Node selection
      nodeSelector:
        kubernetes.io/os: linux
      
      # Tolerations for node taints
      tolerations:
      - key: "kubernetes.io/arch"
        operator: "Equal"
        value: "amd64"
        effect: "NoSchedule"

---
apiVersion: v1
kind: Service
metadata:
  name: adk-analyst-service
  namespace: adk-analyst
  labels:
    app: adk-analyst
    component: jenkins-agent
  annotations:
    cloud.google.com/neg: '{"ingress": true}'
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 80
    targetPort: http
    protocol: TCP
  selector:
    app: adk-analyst

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: adk-analyst-ingress
  namespace: adk-analyst
  labels:
    app: adk-analyst
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "adk-analyst-ip"
    networking.gke.io/managed-certificates: "adk-analyst-ssl-cert"
    kubernetes.io/ingress.allow-http: "false"
spec:
  rules:
  - host: adk-analyst.truxt.ai
    http:
      paths:
      - path: /*
        pathType: ImplementationSpecific
        backend:
          service:
            name: adk-analyst-service
            port:
              number: 80

---
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: adk-analyst-ssl-cert
  namespace: adk-analyst
spec:
  domains:
    - adk-analyst.truxt.ai

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: adk-analyst-pdb
  namespace: adk-analyst
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: adk-analyst

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: adk-analyst-hpa
  namespace: adk-analyst
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: adk-analyst
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
