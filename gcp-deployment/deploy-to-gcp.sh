#!/bin/bash

# Google Cloud Platform Deployment Script for ADK Analyst Jenkins Reader Agent
# Usage: ./gcp-deployment/deploy-to-gcp.sh [deployment-type] [environment]

set -e

# Configuration
PROJECT_ID="truxtsaas"
REGION="us-central1"
SERVICE_NAME="adk-analyst-jenkins-agent"
IMAGE_NAME="gcr.io/truxtsaas/adk-analyst:latest"
SERVICE_ACCOUNT="adk-analyst-service@${PROJECT_ID}.iam.gserviceaccount.com"

# Deployment types
DEPLOYMENT_TYPES=("cloud-run" "gke" "compute-engine")
ENVIRONMENTS=("development" "staging" "production")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

show_usage() {
    echo "Usage: $0 [deployment-type] [environment]"
    echo ""
    echo "Deployment Types:"
    for type in "${DEPLOYMENT_TYPES[@]}"; do
        echo "  - $type"
    done
    echo ""
    echo "Environments:"
    for env in "${ENVIRONMENTS[@]}"; do
        echo "  - $env"
    done
    echo ""
    echo "Examples:"
    echo "  $0 cloud-run production"
    echo "  $0 gke staging"
    echo "  $0 compute-engine development"
}

check_prerequisites() {
    log_step "Checking prerequisites..."
    
    # Check gcloud CLI
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI is not installed"
        echo "Install it from: https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
    
    # Check authentication
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log_error "Not authenticated with gcloud"
        echo "Run: gcloud auth login"
        exit 1
    fi
    
    # Check project
    current_project=$(gcloud config get-value project 2>/dev/null || echo "")
    if [[ "$current_project" != "$PROJECT_ID" ]]; then
        log_warning "Current project: $current_project, expected: $PROJECT_ID"
        log_info "Setting project to $PROJECT_ID"
        gcloud config set project "$PROJECT_ID"
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    log_success "Prerequisites check completed"
}

enable_apis() {
    log_step "Enabling required Google Cloud APIs..."
    
    apis=(
        "run.googleapis.com"
        "container.googleapis.com"
        "compute.googleapis.com"
        "aiplatform.googleapis.com"
        "secretmanager.googleapis.com"
        "logging.googleapis.com"
        "monitoring.googleapis.com"
        "cloudbuild.googleapis.com"
        "artifactregistry.googleapis.com"
    )
    
    for api in "${apis[@]}"; do
        log_info "Enabling $api..."
        gcloud services enable "$api" --quiet
    done
    
    log_success "APIs enabled"
}

create_service_account() {
    log_step "Setting up service account..."
    
    # Create service account if it doesn't exist
    if ! gcloud iam service-accounts describe "$SERVICE_ACCOUNT" &>/dev/null; then
        log_info "Creating service account: $SERVICE_ACCOUNT"
        gcloud iam service-accounts create "adk-analyst-service" \
            --display-name="ADK Analyst Service Account" \
            --description="Service account for ADK Analyst Jenkins Reader Agent"
    else
        log_info "Service account already exists: $SERVICE_ACCOUNT"
    fi
    
    # Grant necessary roles
    roles=(
        "roles/aiplatform.user"
        "roles/ml.developer"
        "roles/logging.logWriter"
        "roles/monitoring.metricWriter"
        "roles/secretmanager.secretAccessor"
        "roles/storage.objectViewer"
    )
    
    for role in "${roles[@]}"; do
        log_info "Granting role: $role"
        gcloud projects add-iam-policy-binding "$PROJECT_ID" \
            --member="serviceAccount:$SERVICE_ACCOUNT" \
            --role="$role" \
            --quiet
    done
    
    log_success "Service account configured"
}

setup_secrets() {
    log_step "Setting up secrets in Secret Manager..."
    
    # Create Jenkins credentials secret if it doesn't exist
    if ! gcloud secrets describe "jenkins-credentials" &>/dev/null; then
        log_info "Creating jenkins-credentials secret..."
        
        # Create a template credentials file
        cat > /tmp/jenkins-credentials.json << EOF
{
  "url": "https://jenkins.truxt.ai",
  "username": "admin",
  "password": "REPLACE_WITH_ACTUAL_PASSWORD"
}
EOF
        
        gcloud secrets create "jenkins-credentials" \
            --data-file="/tmp/jenkins-credentials.json"
        
        rm /tmp/jenkins-credentials.json
        
        log_warning "Please update the jenkins-credentials secret with actual values:"
        echo "gcloud secrets versions add jenkins-credentials --data-file=actual-credentials.json"
    else
        log_info "jenkins-credentials secret already exists"
    fi
    
    log_success "Secrets configured"
}

deploy_cloud_run() {
    local environment=$1
    
    log_step "Deploying to Cloud Run ($environment)..."
    
    # Set service name with environment suffix
    local service_name="${SERVICE_NAME}-${environment}"
    
    # Deploy to Cloud Run
    gcloud run deploy "$service_name" \
        --image="$IMAGE_NAME" \
        --platform=managed \
        --region="$REGION" \
        --service-account="$SERVICE_ACCOUNT" \
        --allow-unauthenticated \
        --port=8000 \
        --memory=4Gi \
        --cpu=2 \
        --timeout=3600 \
        --concurrency=100 \
        --min-instances=1 \
        --max-instances=10 \
        --set-env-vars="GOOGLE_CLOUD_PROJECT=$PROJECT_ID,GOOGLE_CLOUD_LOCATION=$REGION,GOOGLE_GENAI_USE_VERTEXAI=TRUE,LOG_LEVEL=INFO,ENVIRONMENT=$environment" \
        --quiet
    
    # Get service URL
    service_url=$(gcloud run services describe "$service_name" \
        --platform=managed \
        --region="$REGION" \
        --format="value(status.url)")
    
    log_success "Cloud Run deployment completed"
    echo "🌐 Service URL: $service_url"
    
    # Test the deployment
    log_info "Testing deployment..."
    if curl -f "$service_url/health" > /dev/null 2>&1; then
        log_success "Health check passed"
    else
        log_warning "Health check failed - service may still be starting"
    fi
}

deploy_gke() {
    local environment=$1
    
    log_step "Deploying to Google Kubernetes Engine ($environment)..."
    
    # Create GKE cluster if it doesn't exist
    local cluster_name="adk-analyst-cluster-${environment}"
    
    if ! gcloud container clusters describe "$cluster_name" --region="$REGION" &>/dev/null; then
        log_info "Creating GKE cluster: $cluster_name"
        gcloud container clusters create "$cluster_name" \
            --region="$REGION" \
            --num-nodes=2 \
            --machine-type=e2-standard-4 \
            --enable-autoscaling \
            --min-nodes=1 \
            --max-nodes=5 \
            --enable-autorepair \
            --enable-autoupgrade \
            --workload-pool="$PROJECT_ID.svc.id.goog" \
            --quiet
    else
        log_info "GKE cluster already exists: $cluster_name"
    fi
    
    # Get cluster credentials
    gcloud container clusters get-credentials "$cluster_name" --region="$REGION"
    
    # Create namespace
    kubectl create namespace "$environment" --dry-run=client -o yaml | kubectl apply -f -
    
    # Create Kubernetes service account
    kubectl create serviceaccount "adk-analyst-ksa" \
        --namespace="$environment" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # Bind Kubernetes service account to Google service account
    gcloud iam service-accounts add-iam-policy-binding \
        --role roles/iam.workloadIdentityUser \
        --member "serviceAccount:$PROJECT_ID.svc.id.goog[$environment/adk-analyst-ksa]" \
        "$SERVICE_ACCOUNT"
    
    kubectl annotate serviceaccount "adk-analyst-ksa" \
        --namespace="$environment" \
        iam.gke.io/gcp-service-account="$SERVICE_ACCOUNT"
    
    # Apply Kubernetes manifests
    log_info "Applying Kubernetes manifests..."
    
    # Create deployment
    cat << EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: adk-analyst
  namespace: $environment
  labels:
    app: adk-analyst
spec:
  replicas: 2
  selector:
    matchLabels:
      app: adk-analyst
  template:
    metadata:
      labels:
        app: adk-analyst
    spec:
      serviceAccountName: adk-analyst-ksa
      containers:
      - name: adk-analyst
        image: $IMAGE_NAME
        ports:
        - containerPort: 8000
        env:
        - name: GOOGLE_CLOUD_PROJECT
          value: "$PROJECT_ID"
        - name: GOOGLE_CLOUD_LOCATION
          value: "$REGION"
        - name: GOOGLE_GENAI_USE_VERTEXAI
          value: "TRUE"
        - name: LOG_LEVEL
          value: "INFO"
        - name: ENVIRONMENT
          value: "$environment"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: adk-analyst-service
  namespace: $environment
spec:
  selector:
    app: adk-analyst
  ports:
  - port: 80
    targetPort: 8000
  type: LoadBalancer
EOF
    
    # Wait for deployment
    kubectl rollout status deployment/adk-analyst -n "$environment" --timeout=300s
    
    # Get external IP
    log_info "Waiting for external IP..."
    external_ip=""
    while [ -z "$external_ip" ]; do
        external_ip=$(kubectl get service adk-analyst-service -n "$environment" --template="{{range .status.loadBalancer.ingress}}{{.ip}}{{end}}")
        [ -z "$external_ip" ] && sleep 10
    done
    
    log_success "GKE deployment completed"
    echo "🌐 External IP: http://$external_ip"
}

deploy_compute_engine() {
    local environment=$1
    
    log_step "Deploying to Compute Engine ($environment)..."
    
    local instance_name="adk-analyst-${environment}"
    local zone="${REGION}-a"
    
    # Create instance if it doesn't exist
    if ! gcloud compute instances describe "$instance_name" --zone="$zone" &>/dev/null; then
        log_info "Creating Compute Engine instance: $instance_name"
        
        # Create startup script
        cat > /tmp/startup-script.sh << 'EOF'
#!/bin/bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
usermod -aG docker $USER

# Pull and run the container
docker pull ghcr.io/truxt-ai/adk-analyst:latest
docker run -d \
    --name adk-analyst \
    --restart unless-stopped \
    -p 8000:8000 \
    -e GOOGLE_CLOUD_PROJECT=truxtsaas \
    -e GOOGLE_CLOUD_LOCATION=us-central1 \
    -e GOOGLE_GENAI_USE_VERTEXAI=TRUE \
    -e LOG_LEVEL=INFO \
    ghcr.io/truxt-ai/adk-analyst:latest
EOF
        
        gcloud compute instances create "$instance_name" \
            --zone="$zone" \
            --machine-type=e2-standard-4 \
            --image-family=ubuntu-2004-lts \
            --image-project=ubuntu-os-cloud \
            --boot-disk-size=50GB \
            --boot-disk-type=pd-standard \
            --service-account="$SERVICE_ACCOUNT" \
            --scopes=cloud-platform \
            --metadata-from-file startup-script=/tmp/startup-script.sh \
            --tags=http-server,https-server \
            --quiet
        
        rm /tmp/startup-script.sh
        
        # Create firewall rule for port 8000
        if ! gcloud compute firewall-rules describe "allow-adk-analyst" &>/dev/null; then
            gcloud compute firewall-rules create "allow-adk-analyst" \
                --allow tcp:8000 \
                --source-ranges 0.0.0.0/0 \
                --target-tags http-server \
                --description "Allow access to ADK Analyst on port 8000"
        fi
    else
        log_info "Compute Engine instance already exists: $instance_name"
    fi
    
    # Get external IP
    external_ip=$(gcloud compute instances describe "$instance_name" \
        --zone="$zone" \
        --format="value(networkInterfaces[0].accessConfigs[0].natIP)")
    
    log_success "Compute Engine deployment completed"
    echo "🌐 External IP: http://$external_ip:8000"
    
    log_info "Note: It may take a few minutes for the service to start"
}

main() {
    local deployment_type=${1:-}
    local environment=${2:-production}
    
    echo "🚀 ADK Analyst - Google Cloud Deployment"
    echo "========================================"
    echo ""
    
    # Validate arguments
    if [[ -z "$deployment_type" ]]; then
        log_error "Deployment type is required"
        show_usage
        exit 1
    fi
    
    # Validate deployment type
    if [[ ! " ${DEPLOYMENT_TYPES[@]} " =~ " ${deployment_type} " ]]; then
        log_error "Invalid deployment type: $deployment_type"
        show_usage
        exit 1
    fi
    
    # Validate environment
    if [[ ! " ${ENVIRONMENTS[@]} " =~ " ${environment} " ]]; then
        log_error "Invalid environment: $environment"
        show_usage
        exit 1
    fi
    
    log_info "Deployment Type: $deployment_type"
    log_info "Environment: $environment"
    log_info "Project: $PROJECT_ID"
    log_info "Region: $REGION"
    echo ""
    
    # Confirm deployment
    if [[ "$environment" == "production" ]]; then
        read -p "⚠️  You are about to deploy to PRODUCTION. Are you sure? (yes/no): " confirm
        if [[ "$confirm" != "yes" ]]; then
            log_info "Deployment cancelled"
            exit 0
        fi
    fi
    
    # Run deployment steps
    check_prerequisites
    enable_apis
    create_service_account
    setup_secrets
    
    # Deploy based on type
    case $deployment_type in
        "cloud-run")
            deploy_cloud_run "$environment"
            ;;
        "gke")
            deploy_gke "$environment"
            ;;
        "compute-engine")
            deploy_compute_engine "$environment"
            ;;
        *)
            log_error "Unknown deployment type: $deployment_type"
            exit 1
            ;;
    esac
    
    echo ""
    log_success "🎉 Deployment completed successfully!"
    echo ""
    echo "📊 Deployment Summary:"
    echo "  Type: $deployment_type"
    echo "  Environment: $environment"
    echo "  Project: $PROJECT_ID"
    echo "  Region: $REGION"
    echo ""
    echo "📚 Next Steps:"
    echo "  1. Test the deployment using the provided URL"
    echo "  2. Update DNS records if needed"
    echo "  3. Configure monitoring and alerting"
    echo "  4. Set up CI/CD for automated deployments"
}

# Run main function with all arguments
main "$@"
