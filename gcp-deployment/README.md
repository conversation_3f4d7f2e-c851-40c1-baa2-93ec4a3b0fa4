# Google Cloud Platform Deployment Guide

This directory contains all the necessary files and scripts to deploy the ADK Analyst Jenkins Reader Agent to Google Cloud Platform.

## 🚀 Quick Deployment

### Option 1: Cloud Run (Recommended for most use cases)
```bash
# Deploy to Cloud Run
./gcp-deployment/deploy-to-gcp.sh cloud-run production
```

### Option 2: Google Kubernetes Engine (For advanced scaling)
```bash
# Deploy to GKE
./gcp-deployment/deploy-to-gcp.sh gke production
```

### Option 3: Compute Engine (For custom VM requirements)
```bash
# Deploy to Compute Engine
./gcp-deployment/deploy-to-gcp.sh compute-engine production
```

## 📋 Prerequisites

### 1. Google Cloud Setup
```bash
# Install gcloud CLI
curl https://sdk.cloud.google.com | bash
exec -l $SHELL

# Authenticate
gcloud auth login
gcloud auth application-default login

# Set project
gcloud config set project truxtsaas
```

### 2. Enable Required APIs
The deployment script will automatically enable these APIs:
- Cloud Run API
- Kubernetes Engine API
- Compute Engine API
- Vertex AI API
- Secret Manager API
- Cloud Logging API
- Cloud Monitoring API
- Cloud Build API
- Artifact Registry API

### 3. Service Account Setup
The script will create a service account with these roles:
- `roles/aiplatform.user`
- `roles/ml.developer`
- `roles/logging.logWriter`
- `roles/monitoring.metricWriter`
- `roles/secretmanager.secretAccessor`
- `roles/storage.objectViewer`

## 🏗️ Deployment Options

### Cloud Run
**Best for**: Serverless, auto-scaling, pay-per-use
- **Pros**: Zero infrastructure management, automatic scaling, cost-effective
- **Cons**: Cold starts, limited customization
- **Use case**: Production workloads with variable traffic

```bash
./gcp-deployment/deploy-to-gcp.sh cloud-run production
```

**Features**:
- Auto-scaling (1-10 instances)
- 4GB memory, 2 CPU
- 60-minute timeout
- Load balancing included
- HTTPS termination

### Google Kubernetes Engine (GKE)
**Best for**: High availability, complex deployments, microservices
- **Pros**: Full Kubernetes features, high availability, advanced networking
- **Cons**: More complex, higher cost, requires Kubernetes knowledge
- **Use case**: Enterprise deployments, multi-service architectures

```bash
./gcp-deployment/deploy-to-gcp.sh gke production
```

**Features**:
- 3 replicas with auto-scaling (2-10 pods)
- Rolling updates
- Health checks and monitoring
- Ingress with SSL termination
- Workload Identity for security

### Compute Engine
**Best for**: Custom requirements, legacy applications, specific OS needs
- **Pros**: Full VM control, custom configurations, persistent storage
- **Cons**: Manual management, no auto-scaling, higher maintenance
- **Use case**: Custom environments, development/testing

```bash
./gcp-deployment/deploy-to-gcp.sh compute-engine production
```

**Features**:
- e2-standard-4 machine type
- Ubuntu 20.04 LTS
- Docker pre-installed
- Automatic startup script
- External IP with firewall rules

## 🔧 Configuration

### Environment Variables
All deployments use these environment variables:

```bash
GOOGLE_CLOUD_PROJECT=truxtsaas
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_GENAI_USE_VERTEXAI=TRUE
LOG_LEVEL=INFO
MAX_CONCURRENT_REQUESTS=10
RATE_LIMIT_PER_MINUTE=100
ENABLE_AUDIT_LOGGING=true
ENABLE_RATE_LIMITING=true
```

### Secrets Management
Jenkins credentials are stored in Google Secret Manager:

```bash
# Create Jenkins credentials secret
gcloud secrets create jenkins-credentials --data-file=credentials.json

# Update existing secret
gcloud secrets versions add jenkins-credentials --data-file=new-credentials.json
```

**Credentials format** (`credentials.json`):
```json
{
  "url": "https://jenkins.truxt.ai",
  "username": "admin",
  "password": "your-secure-password"
}
```

## 🧪 Testing Deployments

### Health Checks
All deployments include health check endpoints:

```bash
# Test Cloud Run
curl https://adk-analyst-jenkins-agent-production-xxx.a.run.app/health

# Test GKE
curl http://EXTERNAL_IP/health

# Test Compute Engine
curl http://EXTERNAL_IP:8000/health
```

### Integration Testing
```bash
# Test agent functionality
curl -X POST https://your-deployment-url/api/agent \
  -H "Content-Type: application/json" \
  -d '{"query": "What tools do you have available?"}'
```

## 📊 Monitoring and Logging

### Cloud Logging
View logs in Google Cloud Console:
```bash
# Cloud Run logs
gcloud logs read "resource.type=cloud_run_revision" --limit=50

# GKE logs
gcloud logs read "resource.type=k8s_container" --limit=50

# Compute Engine logs
gcloud logs read "resource.type=gce_instance" --limit=50
```

### Cloud Monitoring
Metrics are automatically collected for:
- Request latency
- Error rates
- CPU and memory usage
- Custom application metrics

### Alerting
Set up alerts for:
- High error rates
- Increased latency
- Resource utilization
- Service availability

## 🔐 Security

### Network Security
- **Cloud Run**: HTTPS only, IAM-based access control
- **GKE**: Private cluster option, network policies, Workload Identity
- **Compute Engine**: Firewall rules, service account authentication

### Data Security
- Service account with minimal required permissions
- Secrets stored in Secret Manager
- No sensitive data in container images
- Audit logging enabled

### Access Control
```bash
# Grant access to specific users
gcloud run services add-iam-policy-binding adk-analyst-jenkins-agent-production \
  --member="user:<EMAIL>" \
  --role="roles/run.invoker" \
  --region=us-central1
```

## 🚀 CI/CD with Cloud Build

### Automatic Deployments
Use the included `cloudbuild.yaml` for automated deployments:

```bash
# Create build trigger
gcloud builds triggers create github \
  --repo-name=adk-analyst \
  --repo-owner=truxt-ai \
  --branch-pattern="^main$" \
  --build-config=gcp-deployment/cloudbuild.yaml
```

### Manual Build
```bash
# Trigger manual build
gcloud builds submit --config=gcp-deployment/cloudbuild.yaml .
```

## 🔄 Updates and Rollbacks

### Cloud Run
```bash
# Deploy new version
gcloud run deploy adk-analyst-jenkins-agent-production \
  --image=gcr.io/truxtsaas/adk-analyst:new-version

# Rollback
gcloud run services update-traffic adk-analyst-jenkins-agent-production \
  --to-revisions=PREVIOUS_REVISION=100
```

### GKE
```bash
# Update deployment
kubectl set image deployment/adk-analyst adk-analyst=gcr.io/truxtsaas/adk-analyst:new-version

# Rollback
kubectl rollout undo deployment/adk-analyst
```

### Compute Engine
```bash
# Update via SSH
gcloud compute ssh adk-analyst-production --zone=us-central1-a
docker pull gcr.io/truxtsaas/adk-analyst:new-version
docker stop adk-analyst && docker rm adk-analyst
docker run -d --name adk-analyst --restart unless-stopped -p 8000:8000 gcr.io/truxtsaas/adk-analyst:new-version
```

## 💰 Cost Optimization

### Cloud Run
- Pay only for requests
- Automatic scaling to zero
- No idle costs

### GKE
- Use preemptible nodes for cost savings
- Enable cluster autoscaling
- Right-size node pools

### Compute Engine
- Use committed use discounts
- Schedule instances for development environments
- Use appropriate machine types

## 🆘 Troubleshooting

### Common Issues

#### 1. Authentication Errors
```bash
# Re-authenticate
gcloud auth login
gcloud auth application-default login
```

#### 2. Permission Denied
```bash
# Check IAM roles
gcloud projects get-iam-policy truxtsaas
```

#### 3. Service Unavailable
```bash
# Check service status
gcloud run services describe adk-analyst-jenkins-agent-production --region=us-central1
```

#### 4. Build Failures
```bash
# Check build logs
gcloud builds log BUILD_ID
```

### Support
- Check Google Cloud Status: https://status.cloud.google.com/
- Review logs in Cloud Console
- Use `gcloud` CLI for debugging
- Contact support through Cloud Console

## 📚 Additional Resources

- [Cloud Run Documentation](https://cloud.google.com/run/docs)
- [GKE Documentation](https://cloud.google.com/kubernetes-engine/docs)
- [Compute Engine Documentation](https://cloud.google.com/compute/docs)
- [Vertex AI Documentation](https://cloud.google.com/vertex-ai/docs)
- [Secret Manager Documentation](https://cloud.google.com/secret-manager/docs)

---

**Ready to deploy? Choose your deployment method and run the appropriate command!** 🚀
