# Google Cloud Build configuration for ADK Analyst Jenkins Reader Agent
# This file automates the build and deployment process

steps:
  # Step 1: Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-image'
    args:
      - 'build'
      - '-f'
      - 'Dockerfile.adk'
      - '-t'
      - 'gcr.io/$PROJECT_ID/adk-analyst:$BUILD_ID'
      - '-t'
      - 'gcr.io/$PROJECT_ID/adk-analyst:latest'
      - '.'
    timeout: '600s'

  # Step 2: Push the image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-image'
    args:
      - 'push'
      - 'gcr.io/$PROJECT_ID/adk-analyst:$BUILD_ID'
    waitFor: ['build-image']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-latest'
    args:
      - 'push'
      - 'gcr.io/$PROJECT_ID/adk-analyst:latest'
    waitFor: ['build-image']

  # Step 3: Run security scan
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'security-scan'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        gcloud container images scan gcr.io/$PROJECT_ID/adk-analyst:$BUILD_ID \
          --format='value(response.scan.analysisKind)' || true
    waitFor: ['push-image']

  # Step 4: Deploy to Cloud Run (staging)
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-staging'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        gcloud run deploy adk-analyst-staging \
          --image gcr.io/$PROJECT_ID/adk-analyst:$BUILD_ID \
          --platform managed \
          --region us-central1 \
          --service-account adk-analyst-service@$PROJECT_ID.iam.gserviceaccount.com \
          --allow-unauthenticated \
          --port 8000 \
          --memory 4Gi \
          --cpu 2 \
          --timeout 3600 \
          --concurrency 100 \
          --min-instances 1 \
          --max-instances 5 \
          --set-env-vars "GOOGLE_CLOUD_PROJECT=$PROJECT_ID,GOOGLE_CLOUD_LOCATION=us-central1,GOOGLE_GENAI_USE_VERTEXAI=TRUE,LOG_LEVEL=INFO,ENVIRONMENT=staging" \
          --quiet
    waitFor: ['push-image', 'security-scan']

  # Step 5: Run integration tests against staging
  - name: 'gcr.io/cloud-builders/curl'
    id: 'test-staging'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        # Get staging URL
        STAGING_URL=$(gcloud run services describe adk-analyst-staging \
          --platform managed \
          --region us-central1 \
          --format "value(status.url)")
        
        echo "Testing staging deployment at: $STAGING_URL"
        
        # Wait for service to be ready
        sleep 30
        
        # Test health endpoint
        curl -f "$STAGING_URL/health" || exit 1
        
        # Test basic functionality (if health endpoint exists)
        echo "Staging tests passed"
    waitFor: ['deploy-staging']

  # Step 6: Deploy to production (only on main branch)
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-production'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if [ "$BRANCH_NAME" = "main" ]; then
          echo "Deploying to production..."
          gcloud run deploy adk-analyst-production \
            --image gcr.io/$PROJECT_ID/adk-analyst:$BUILD_ID \
            --platform managed \
            --region us-central1 \
            --service-account adk-analyst-service@$PROJECT_ID.iam.gserviceaccount.com \
            --allow-unauthenticated \
            --port 8000 \
            --memory 4Gi \
            --cpu 2 \
            --timeout 3600 \
            --concurrency 100 \
            --min-instances 2 \
            --max-instances 10 \
            --set-env-vars "GOOGLE_CLOUD_PROJECT=$PROJECT_ID,GOOGLE_CLOUD_LOCATION=us-central1,GOOGLE_GENAI_USE_VERTEXAI=TRUE,LOG_LEVEL=INFO,ENVIRONMENT=production" \
            --quiet
        else
          echo "Skipping production deployment (not main branch)"
        fi
    waitFor: ['test-staging']

  # Step 7: Update GKE deployment (optional)
  - name: 'gcr.io/cloud-builders/kubectl'
    id: 'deploy-gke'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if [ "$BRANCH_NAME" = "main" ] && [ "$_DEPLOY_GKE" = "true" ]; then
          echo "Updating GKE deployment..."
          
          # Get GKE credentials
          gcloud container clusters get-credentials adk-analyst-cluster-production --region us-central1
          
          # Update deployment image
          kubectl set image deployment/adk-analyst \
            adk-analyst=gcr.io/$PROJECT_ID/adk-analyst:$BUILD_ID \
            -n adk-analyst
          
          # Wait for rollout
          kubectl rollout status deployment/adk-analyst -n adk-analyst --timeout=300s
        else
          echo "Skipping GKE deployment"
        fi
    waitFor: ['deploy-production']

  # Step 8: Send notification
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'notify'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "Build completed successfully!"
        echo "Build ID: $BUILD_ID"
        echo "Branch: $BRANCH_NAME"
        echo "Commit: $COMMIT_SHA"
        
        # You can add Slack/email notifications here
        # Example: curl -X POST -H 'Content-type: application/json' \
        #   --data '{"text":"ADK Analyst deployed successfully"}' \
        #   $SLACK_WEBHOOK_URL
    waitFor: ['deploy-production', 'deploy-gke']

# Build options
options:
  # Use high-performance machine type for faster builds
  machineType: 'E2_HIGHCPU_8'
  
  # Disk size for build
  diskSizeGb: 100
  
  # Logging options
  logging: CLOUD_LOGGING_ONLY
  
  # Substitute variables
  substitution_option: 'ALLOW_LOOSE'

# Substitutions (can be overridden)
substitutions:
  _DEPLOY_GKE: 'false'  # Set to 'true' to enable GKE deployment

# Timeout for entire build
timeout: '1800s'  # 30 minutes

# Build triggers (when this file is used with Cloud Build triggers)
# These are configured in the Cloud Console or via gcloud commands
# trigger:
#   github:
#     owner: truxt-ai
#     name: adk-analyst
#     push:
#       branch: ^main$|^develop$

# IAM permissions required for this build:
# - Cloud Run Admin
# - Container Registry Service Agent
# - Kubernetes Engine Admin (if using GKE)
# - Service Account User
# - Security Scanner Service Agent
