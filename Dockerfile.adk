# Jenkins Reader Agent with ADK Web - Dockerfile
FROM python:3.12-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV GOOGLE_GENAI_USE_VERTEXAI=1
ENV GOOGLE_CLOUD_PROJECT=truxtsaas
ENV GOOGLE_CLOUD_LOCATION=us-central1

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install Google ADK
RUN pip install --no-cache-dir google-adk

# Copy application code
COPY jenkins_agent/ ./jenkins_agent/
COPY adk.yaml .
COPY .env .

# Copy service account key (this will be mounted as a secret in production)
COPY service-account-key.json ./service-account-key.json

# Set Google Application Credentials
ENV GOOGLE_APPLICATION_CREDENTIALS=/app/service-account-key.json

# Create non-root user
RUN useradd -m -u 1000 jenkins-agent && \
    chown -R jenkins-agent:jenkins-agent /app && \
    chmod 600 /app/service-account-key.json

USER jenkins-agent

# Expose ADK Web port
EXPOSE 8000

# Health check for ADK Web
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Default command runs ADK Web
CMD ["python", "-m", "google.adk.cli", "web", "--host", "0.0.0.0", "--port", "8000"]
