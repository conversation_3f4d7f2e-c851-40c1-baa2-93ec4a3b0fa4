# ADK Analyst - Jenkins Reader Agent

[![Docker Build](https://img.shields.io/badge/docker-ready-blue)](https://github.com/truxt-ai/adk-analyst)
[![Python 3.12+](https://img.shields.io/badge/python-3.12+-blue)](https://www.python.org/downloads/)
[![Google ADK](https://img.shields.io/badge/Google-ADK-4285f4)](https://cloud.google.com/vertex-ai)

A comprehensive Jenkins analysis and reporting agent built with Google ADK (Agent Development Kit). This enterprise-grade solution provides safe, read-only access to Jenkins infrastructure with advanced AI-powered analysis capabilities.

## 🚀 Features

- **🔒 Read-Only Access**: Safe Jenkins infrastructure analysis without modification risks
- **🛠️ Comprehensive Tools**: 6 specialized tools for different Jenkins operations
- **🏢 Enterprise Security**: Built-in authentication, audit logging, and rate limiting
- **🖥️ Multiple Interfaces**: CLI, Web UI, and API access
- **☁️ Google Cloud Integration**: Leverages Vertex AI and Google Cloud services
- **🐳 Docker Support**: Containerized deployment ready
- **🤖 AI-Powered**: Uses Gemini 2.5 Pro/Flash for intelligent analysis
- **📊 Real-time Analytics**: Live Jenkins data with performance metrics

## 📋 Table of Contents

- [Quick Start](#quick-start)
- [Installation](#installation)
- [Usage](#usage)
- [Tools & Capabilities](#tools--capabilities)
- [Configuration](#configuration)
- [Docker Deployment](#docker-deployment)
- [Development](#development)
- [Documentation](#documentation)
- [Contributing](#contributing)

## 🚀 Quick Start

### Prerequisites

- Python 3.12+
- Google Cloud Project with Vertex AI enabled
- Jenkins server access credentials
- Docker (optional, for containerized deployment)

### Installation

1. **Clone the repository:**
```bash
<NAME_EMAIL>:truxt-ai/adk-analyst.git
cd adk-analyst
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Configure environment:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Set up Google Cloud credentials:**
```bash
# Place your service account key in the project root
# DO NOT commit this file - it's in .gitignore
export GOOGLE_APPLICATION_CREDENTIALS=./service-account-key.json
```

### Quick Test

```bash
# Test the agent
python test_simple_cli.py

# Start web interface
python -m google.adk.cli web . --port 8000
```

## �� Usage

### CLI Mode
```bash
python -m jenkins_agent.main
```

### Web Interface
```bash
python -m google.adk.cli web . --port 8000
# Access at http://localhost:8000
```

### Docker Deployment
```bash
# Build image
docker build -f Dockerfile.adk -t jenkins-reader-agent .

# Run container
docker run -p 8000:8000 jenkins-reader-agent

# Access at http://localhost:8000
```

## 🛠️ Tools & Capabilities

| Tool | Description | Use Case |
|------|-------------|----------|
| `validate_jenkins_connection` | Test connectivity and retrieve server info | Health checks, connectivity validation |
| `get_jenkins_server_status` | Get comprehensive server status and health | System monitoring, performance analysis |
| `get_jenkins_jobs` | List and filter Jenkins jobs | Job discovery, inventory management |
| `get_job_config` | Retrieve job configurations | Configuration analysis, compliance checking |
| `get_build_history` | Analyze build history and trends | Performance optimization, trend analysis |
| `get_artifacts` | Access build artifacts and metadata | Artifact management, deployment tracking |

### AI Models

- **Gemini 2.5 Pro**: Advanced model for complex analysis and detailed reporting
- **Gemini 2.5 Flash**: Fast model for quick queries and real-time operations

## ⚙️ Configuration

### Environment Variables

```bash
# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_GENAI_USE_VERTEXAI=TRUE

# Jenkins Configuration  
JENKINS_CREDENTIALS_SECRET=jenkins-credentials
ALLOWED_JENKINS_DOMAINS=jenkins.example.com,localhost
JENKINS_PASSWORD=your-jenkins-password

# Application Configuration
LOG_LEVEL=INFO
MAX_CONCURRENT_REQUESTS=10
RATE_LIMIT_PER_MINUTE=100

# Security Configuration
ENABLE_AUDIT_LOGGING=true
ENABLE_RATE_LIMITING=true
MAX_RESULTS_PER_QUERY=1000
```

### ADK Configuration

The agent is configured via `adk.yaml`. Key settings:

- **Models**: Gemini 2.5 Pro (default) and Flash (fast queries)
- **Security**: Read-only access, authentication required
- **Tools**: All 6 Jenkins analysis tools enabled
- **Enterprise Features**: IAM integration, audit logging, monitoring

## 🐳 Docker Deployment

### Build and Run

```bash
# Build the image
docker build -f Dockerfile.adk -t jenkins-reader-agent:latest .

# Run with environment variables
docker run -d \
  -p 8000:8000 \
  -e GOOGLE_CLOUD_PROJECT=your-project \
  -v /path/to/service-account-key.json:/app/service-account-key.json \
  jenkins-reader-agent:latest
```

### Docker Compose

```bash
# Development
docker-compose up -d

# Production
docker-compose -f docker-compose.prod.yml up -d
```

### Health Checks

The container includes built-in health checks:
- Endpoint: `http://localhost:8000/health`
- Interval: 30s
- Timeout: 10s
- Retries: 3

## 🔧 Development

### Setup Development Environment

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install development dependencies
pip install pytest black isort mypy
```

### Running Tests

```bash
# Basic functionality test
python test_simple_cli.py

# Comprehensive testing
python test_cli.py

# Run all tests
pytest
```

### Code Quality

```bash
# Format code
black jenkins_agent/
isort jenkins_agent/

# Type checking
mypy jenkins_agent/
```

## 📚 Documentation

- [📖 Setup Guide](docs/setup.md) - Detailed installation and configuration
- [🏗️ Design Overview](docs/design.md) - Architecture and design decisions  
- [📊 Test Results](TEST_RESULTS.md) - Comprehensive testing validation
- [🔧 Development Guide](docs/development.md) - Contributing and development setup

## 🏢 Enterprise Features

- **Google Cloud IAM Integration**: Secure authentication and authorization
- **Structured Logging**: Comprehensive audit trails and monitoring
- **Auto-scaling**: Kubernetes-ready for high availability
- **Compliance**: Built-in security and audit features
- **Performance Optimization**: Efficient resource usage and caching
- **Error Handling**: Robust error recovery and reporting

## 🔐 Security

- **Read-Only Access**: No write operations to Jenkins
- **Input Validation**: All inputs sanitized and validated
- **Rate Limiting**: Prevents overwhelming Jenkins servers
- **Audit Logging**: All operations logged for compliance
- **Credential Management**: Secure handling of sensitive data

## 📈 Performance

- **Response Time**: < 500ms for typical operations
- **Throughput**: 100+ requests per minute
- **Memory Usage**: Optimized for production workloads
- **Scalability**: Horizontal scaling support

## 🤝 Contributing

This is a private repository. For internal contributors:

1. Create a feature branch
2. Make your changes
3. Run tests and quality checks
4. Submit a pull request

## 📄 License

Private repository - All rights reserved by Truxt AI.

---

**Built with ❤️ by Truxt AI using Google ADK**
